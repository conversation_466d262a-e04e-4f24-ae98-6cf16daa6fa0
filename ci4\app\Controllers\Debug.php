<?php

namespace App\Controllers;

use App\Models\ArtikelModel;

class Debug extends BaseController
{
    public function pagination()
    {
        $model = new ArtikelModel();
        
        // Get pagination info
        $total = $model->countAll();
        $perPage = 10;
        $totalPages = ceil($total / $perPage);
        
        echo "<h1>Debug Pagination</h1>";
        echo "<h2>Database Info:</h2>";
        echo "<ul>";
        echo "<li>Total Articles: <strong>{$total}</strong></li>";
        echo "<li>Per Page: <strong>{$perPage}</strong></li>";
        echo "<li>Total Pages: <strong>{$totalPages}</strong></li>";
        echo "</ul>";
        
        echo "<h2>Articles List:</h2>";
        $articles = $model->findAll();
        echo "<ol>";
        foreach ($articles as $article) {
            echo "<li>ID: {$article['id']} - {$article['judul']}</li>";
        }
        echo "</ol>";
        
        echo "<h2>Pagination Test:</h2>";
        $paginatedArticles = $model->paginate(10);
        $pager = $model->pager;
        
        echo "<ul>";
        echo "<li>Current Page: <strong>{$pager->getCurrentPage()}</strong></li>";
        echo "<li>Page Count: <strong>{$pager->getPageCount()}</strong></li>";
        echo "<li>Per Page: <strong>{$pager->getPerPage()}</strong></li>";
        echo "<li>Total: <strong>{$pager->getTotal()}</strong></li>";
        echo "<li>Has Previous: " . ($pager->hasPrevious() ? 'Yes' : 'No') . "</li>";
        echo "<li>Has Next: " . ($pager->hasNext() ? 'Yes' : 'No') . "</li>";
        echo "</ul>";
        
        echo "<h2>Pagination Links:</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
        echo $pager->links('custom_pagination');
        echo "</div>";
        
        echo "<p><a href='" . base_url('/admin/artikel') . "'>← Back to Admin</a></p>";
    }
}
