<?php

namespace App\Controllers;

use App\Models\ArtikelModel;

class Debug extends BaseController
{
    public function pagination()
    {
        $model = new ArtikelModel();
        $page = $this->request->getVar('page') ?? 1;

        echo "<h1>Debug Pagination - Page {$page}</h1>";

        // Test pagination for each page
        echo "<h2>Page Navigation:</h2>";
        echo "<p>";
        echo "<a href='?page=1' style='margin-right: 10px;'>Page 1</a>";
        echo "<a href='?page=2' style='margin-right: 10px;'>Page 2</a>";
        echo "<a href='?page=3' style='margin-right: 10px;'>Page 3</a>";
        echo "</p>";

        // Get pagination info
        $total = $model->countAll();
        $perPage = 10;
        $totalPages = ceil($total / $perPage);

        echo "<h2>Database Info:</h2>";
        echo "<ul>";
        echo "<li>Total Articles: <strong>{$total}</strong></li>";
        echo "<li>Per Page: <strong>{$perPage}</strong></li>";
        echo "<li>Total Pages: <strong>{$totalPages}</strong></li>";
        echo "<li>Current Page: <strong>{$page}</strong></li>";
        echo "</ul>";

        // Test pagination
        echo "<h2>Pagination Test for Page {$page}:</h2>";
        $paginatedArticles = $model->orderBy('id', 'DESC')->paginate(10);
        $pager = $model->pager;

        echo "<ul>";
        echo "<li>Current Page: <strong>{$pager->getCurrentPage()}</strong></li>";
        echo "<li>Page Count: <strong>{$pager->getPageCount()}</strong></li>";
        echo "<li>Per Page: <strong>{$pager->getPerPage()}</strong></li>";
        echo "<li>Total: <strong>{$pager->getTotal()}</strong></li>";
        echo "<li>Has Previous: " . ($pager->getCurrentPage() > 1 ? 'Yes' : 'No') . "</li>";
        echo "<li>Has Next: " . ($pager->getCurrentPage() < $pager->getPageCount() ? 'Yes' : 'No') . "</li>";
        echo "</ul>";

        echo "<h2>Articles on Page {$page}:</h2>";
        echo "<ol>";
        foreach ($paginatedArticles as $article) {
            echo "<li>ID: {$article['id']} - {$article['judul']}</li>";
        }
        echo "</ol>";

        echo "<h2>Pagination Links:</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
        echo $pager->links('simple_pagination');
        echo "</div>";

        echo "<p><a href='" . base_url('/admin/artikel') . "'>← Back to Admin</a></p>";
    }
}
