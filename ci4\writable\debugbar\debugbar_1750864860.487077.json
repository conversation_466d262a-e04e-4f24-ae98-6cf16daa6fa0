{"url": "http://localhost:8080/index.php/admin/artikel/delete/1", "method": "GET", "isAJAX": false, "startTime": **********.330097, "totalTime": 130.39999999999998, "totalMemory": "5.475", "segmentDuration": 20, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.334927, "duration": 0.027981996536254883}, {"name": "Required Before Filters", "component": "Timer", "start": **********.362911, "duration": 0.003365039825439453}, {"name": "Routing", "component": "Timer", "start": **********.366281, "duration": 0.005399942398071289}, {"name": "Before Filters", "component": "Timer", "start": **********.372522, "duration": 0.*****************}, {"name": "Controller", "component": "Timer", "start": **********.380427, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.380428, "duration": 0.0010738372802734375}, {"name": "After Filters", "component": "Timer", "start": **********.459886, "duration": 6.9141387939453125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.459897, "duration": 0.0006649494171142578}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(30 total Queries, 30 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "4.62 ms", "sql": "DELETE <strong>FROM</strong> `artikel`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;1&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2848", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:521", "function": "        CodeIgniter\\Database\\BaseBuilder->delete()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1157", "function": "        CodeIgniter\\Model->doDelete()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Artikel.php:88", "function": "        CodeIgniter\\BaseModel->delete()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Artikel.php:88", "qid": "df30f3194c164a781c6d94ac4acdd939"}, {"hover": "", "class": "", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>ORDER</strong> <strong>BY</strong> `display_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:675", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:27", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:27", "qid": "209adb3c977deda0a19a6b4f23f26aea"}, {"hover": "", "class": "", "duration": "2.28 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 3\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;15&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "e4a323a4229950c45c5f2cc94d471026"}, {"hover": "", "class": "", "duration": "1.91 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 4\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;10&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "b4cab380af39553155fccf70fb383d7e"}, {"hover": "", "class": "", "duration": "1.74 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 5\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;16&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "372e6bf49072cae56f4433689286c9a0"}, {"hover": "", "class": "", "duration": "1.78 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 6\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;11&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "7c0d4321d784294ed643da82d764c17d"}, {"hover": "", "class": "", "duration": "1.88 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 7\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;17&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "0a4ca652175202b51222c6cbd093db3b"}, {"hover": "", "class": "", "duration": "1.84 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 8\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;12&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "2d1a78702de1f92fddb081c1c8ccea08"}, {"hover": "", "class": "", "duration": "1.72 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 9\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;18&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "93dabcdb83c3764afe5a747d3a66de0a"}, {"hover": "", "class": "", "duration": "1.63 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 10\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;13&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "76142430078089b7f21ad238670e3140"}, {"hover": "", "class": "", "duration": "1.95 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 11\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;19&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "52fa19ab7d7d9d66f220dc04df614524"}, {"hover": "", "class": "", "duration": "1.87 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 12\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;20&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "29fabfb0e38924282fa8c85e680a63ea"}, {"hover": "", "class": "", "duration": "1.86 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 13\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;21&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "2ec354b4bef762ef1342bf7e0e957675"}, {"hover": "", "class": "", "duration": "1.74 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 14\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;22&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "1daf18e44b402e12aeb724c07ddbe85a"}, {"hover": "", "class": "", "duration": "1.58 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 15\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;23&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "f41ac1929a8869fb823d86bb3c28215b"}, {"hover": "", "class": "", "duration": "1.91 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 16\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;24&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "c9fe9055f7e771d4bfc45cebaed24f81"}, {"hover": "", "class": "", "duration": "1.76 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 17\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;25&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "d09f9aa3cd519c9541bc6284feb25dd8"}, {"hover": "", "class": "", "duration": "1.63 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 18\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;26&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "f10b9729ec6627f4701ebdbc0958dd22"}, {"hover": "", "class": "", "duration": "1.72 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 19\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;27&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "257625fe04620abed839c89d5506e601"}, {"hover": "", "class": "", "duration": "1.88 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 20\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;28&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "bbeb549f3bf11dd027837a7dcfd60aee"}, {"hover": "", "class": "", "duration": "1.93 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 21\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;29&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "30890485f2d071d12934da1d28a52e97"}, {"hover": "", "class": "", "duration": "1.95 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 22\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;30&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "aba24300e8b4adfd1141e9f46888e862"}, {"hover": "", "class": "", "duration": "1.73 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 23\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;31&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "146cae80c42b9fe1e0783756c68ff7c3"}, {"hover": "", "class": "", "duration": "1.74 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 24\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;32&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "f8f9840a3b346b0fcd0a9e6fa4b8ade7"}, {"hover": "", "class": "", "duration": "1.88 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 25\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;33&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "79e65de11a156bb721f1bb77181cadc6"}, {"hover": "", "class": "", "duration": "1.84 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 26\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;34&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "48147a84e7e47698aba6ce69cf48c123"}, {"hover": "", "class": "", "duration": "1.88 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 27\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;35&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "0f242ce54f1d2b6065888a86b42d0bf8"}, {"hover": "", "class": "", "duration": "1.74 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 28\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;36&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "a2db0cd574fd8c2cde0bb1f528a4faab"}, {"hover": "", "class": "", "duration": "1.82 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 29\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;37&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "7f88960e5b8e0ce7d68e567501c2743f"}, {"hover": "", "class": "", "duration": "1.89 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 30\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;38&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "5bcc32a96638d022093dc18389757f02"}]}, "badgeValue": 30, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.394945, "duration": "0.001925"}, {"name": "Query", "component": "Database", "start": **********.397896, "duration": "0.004617", "query": "DELETE <strong>FROM</strong> `artikel`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;1&#039;)"}, {"name": "Query", "component": "Database", "start": **********.40262, "duration": "0.000529", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>ORDER</strong> <strong>BY</strong> `display_order` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.40514, "duration": "0.002282", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 3\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;15&#039;)"}, {"name": "Query", "component": "Database", "start": **********.407565, "duration": "0.001910", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 4\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;10&#039;)"}, {"name": "Query", "component": "Database", "start": **********.409587, "duration": "0.001737", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 5\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;16&#039;)"}, {"name": "Query", "component": "Database", "start": **********.411404, "duration": "0.001782", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 6\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;11&#039;)"}, {"name": "Query", "component": "Database", "start": **********.413261, "duration": "0.001876", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 7\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;17&#039;)"}, {"name": "Query", "component": "Database", "start": **********.415286, "duration": "0.001840", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 8\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;12&#039;)"}, {"name": "Query", "component": "Database", "start": **********.417237, "duration": "0.001718", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 9\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;18&#039;)"}, {"name": "Query", "component": "Database", "start": **********.419043, "duration": "0.001629", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 10\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;13&#039;)"}, {"name": "Query", "component": "Database", "start": **********.420746, "duration": "0.001948", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 11\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;19&#039;)"}, {"name": "Query", "component": "Database", "start": **********.42289, "duration": "0.001870", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 12\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;20&#039;)"}, {"name": "Query", "component": "Database", "start": **********.42488, "duration": "0.001859", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 13\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;21&#039;)"}, {"name": "Query", "component": "Database", "start": **********.426819, "duration": "0.001736", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 14\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;22&#039;)"}, {"name": "Query", "component": "Database", "start": **********.428633, "duration": "0.001581", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 15\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;23&#039;)"}, {"name": "Query", "component": "Database", "start": **********.430305, "duration": "0.001914", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 16\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;24&#039;)"}, {"name": "Query", "component": "Database", "start": **********.432312, "duration": "0.001761", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 17\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;25&#039;)"}, {"name": "Query", "component": "Database", "start": **********.434148, "duration": "0.001630", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 18\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;26&#039;)"}, {"name": "Query", "component": "Database", "start": **********.43587, "duration": "0.001715", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 19\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;27&#039;)"}, {"name": "Query", "component": "Database", "start": **********.43766, "duration": "0.001885", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 20\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;28&#039;)"}, {"name": "Query", "component": "Database", "start": **********.439698, "duration": "0.001926", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 21\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;29&#039;)"}, {"name": "Query", "component": "Database", "start": **********.441751, "duration": "0.001950", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 22\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;30&#039;)"}, {"name": "Query", "component": "Database", "start": **********.443794, "duration": "0.001733", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 23\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;31&#039;)"}, {"name": "Query", "component": "Database", "start": **********.445614, "duration": "0.001736", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 24\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;32&#039;)"}, {"name": "Query", "component": "Database", "start": **********.447473, "duration": "0.001877", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 25\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;33&#039;)"}, {"name": "Query", "component": "Database", "start": **********.449445, "duration": "0.001843", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 26\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;34&#039;)"}, {"name": "Query", "component": "Database", "start": **********.451376, "duration": "0.001876", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 27\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;35&#039;)"}, {"name": "Query", "component": "Database", "start": **********.453336, "duration": "0.001743", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 28\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;36&#039;)"}, {"name": "Query", "component": "Database", "start": **********.455286, "duration": "0.001820", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 29\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;37&#039;)"}, {"name": "Query", "component": "Database", "start": **********.457239, "duration": "0.001886", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 30\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;38&#039;)"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 149 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RedirectResponse.php", "name": "RedirectResponse.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Artikel.php", "name": "Artikel.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\Auth.php", "name": "Auth.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 149, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Artikel", "method": "delete", "paramCount": 1, "truePCount": 1, "params": [{"name": "$id = ", "value": "1"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "services", "handler": "\\App\\Controllers\\Page::services"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\artikel::index"}, {"method": "GET", "route": "artikel/(.*)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "user", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 15, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "12.33", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.67", "count": 30}}}, "badgeValue": 31, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.350574, "duration": 0.012330055236816406}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.40252, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.403152, "duration": 1.5020370483398438e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.407427, "duration": 3.981590270996094e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.409479, "duration": 2.002716064453125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.411327, "duration": 1.4066696166992188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.413188, "duration": 1.0967254638671875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.415146, "duration": 2.9802322387695312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.417131, "duration": 2.002716064453125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.418958, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.420675, "duration": 1.0967254638671875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.422708, "duration": 5.793571472167969e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.424765, "duration": 2.193450927734375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.426742, "duration": 1.3828277587890625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.428557, "duration": 1.3113021850585938e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.430218, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.432222, "duration": 1.71661376953125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.434075, "duration": 1.2874603271484375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.43578, "duration": 1.1920928955078125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.437587, "duration": 1.1920928955078125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.439551, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.44163, "duration": 2.6226043701171875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.443704, "duration": 1.811981201171875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.44553, "duration": 1.4066696166992188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.447355, "duration": 2.4080276489257812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.449354, "duration": 1.811981201171875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.451291, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.453255, "duration": 1.4066696166992188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.455092, "duration": 7.510185241699219e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.457111, "duration": 2.9087066650390625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.45913, "duration": 2.7894973754882812e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>**********</pre>", "_ci_previous_url": "http://localhost:8080/index.php/admin/artikel?page=1", "user_id": "4", "user_name": "admin", "user_email": "<EMAIL>", "logged_in": "<pre>1</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/index.php/admin/artikel?page=1", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id,en-US;q=0.9,en;q=0.8", "Cookie": "ci_session=e555dfe34a00c762c19a6f46353cc95c"}, "cookies": {"ci_session": "e555dfe34a00c762c19a6f46353cc95c"}, "request": "HTTP/1.1", "response": {"statusCode": 302, "reason": "Found", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8", "Location": "http://localhost:8080/index.php/admin/artikel"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}