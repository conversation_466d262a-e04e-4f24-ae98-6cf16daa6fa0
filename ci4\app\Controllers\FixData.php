<?php

namespace App\Controllers;

use App\Models\ArtikelModel;

class FixData extends BaseController
{
    public function articles()
    {
        $model = new ArtikelModel();
        
        // Cek jumlah artikel saat ini
        $total = $model->countAll();
        
        echo "<h1>Fix Data Artikel</h1>";
        echo "<p>Total artikel saat ini: <strong>{$total}</strong></p>";
        
        if ($total < 25) {
            // Tambah artikel sampai 25
            $needed = 25 - $total;
            echo "<p>Menambahkan {$needed} artikel...</p>";
            
            for ($i = $total + 1; $i <= 25; $i++) {
                $data = [
                    'judul' => "Artikel Sample $i",
                    'isi' => "Ini adalah isi artikel sample nomor $i. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.",
                    'slug' => "artikel-sample-$i",
                    'status' => 1,
                    'gambar' => '',
                    'display_order' => $i
                ];
                $model->insert($data);
            }
            
            $newTotal = $model->countAll();
            echo "<p style='color: green;'>✅ Berhasil menambahkan artikel!</p>";
            echo "<p>Total artikel sekarang: <strong>{$newTotal}</strong></p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Artikel sudah cukup (≥ 25).</p>";
        }
        
        // Tampilkan info pagination
        $perPage = 10;
        $totalPages = ceil($model->countAll() / $perPage);
        echo "<h2>Info Pagination:</h2>";
        echo "<ul>";
        echo "<li>Total Artikel: <strong>" . $model->countAll() . "</strong></li>";
        echo "<li>Per Halaman: <strong>{$perPage}</strong></li>";
        echo "<li>Total Halaman: <strong>{$totalPages}</strong></li>";
        echo "</ul>";
        
        echo "<p><a href='" . base_url('/admin/artikel') . "'>← Test Pagination</a></p>";
        echo "<p><a href='" . base_url('/debug/pagination') . "'>Debug Pagination</a></p>";
    }
}
