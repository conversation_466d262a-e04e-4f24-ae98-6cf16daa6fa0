<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table            = 'user';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['username', 'useremail', 'userpassword'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = false;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [
        'username' => 'required|min_length[3]|max_length[200]|is_unique[user.username]',
        'useremail' => 'permit_empty|valid_email|max_length[200]|is_unique[user.useremail]',
        'userpassword' => 'required|min_length[6]|max_length[200]'
    ];
    protected $validationMessages   = [
        'username' => [
            'required' => 'Username harus diisi',
            'min_length' => 'Username minimal 3 karakter',
            'max_length' => 'Username maksimal 200 karakter',
            'is_unique' => 'Username sudah digunakan'
        ],
        'useremail' => [
            'valid_email' => 'Format email tidak valid',
            'max_length' => 'Email maksimal 200 karakter',
            'is_unique' => 'Email sudah digunakan'
        ],
        'userpassword' => [
            'required' => 'Password harus diisi',
            'min_length' => 'Password minimal 6 karakter',
            'max_length' => 'Password maksimal 200 karakter'
        ]
    ];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];
}
