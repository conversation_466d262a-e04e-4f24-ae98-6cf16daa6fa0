{"url": "http://localhost:8080/index.php/admin/artikel/delete/12", "method": "GET", "isAJAX": false, "startTime": **********.95938, "totalTime": 135.8, "totalMemory": "5.483", "segmentDuration": 20, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.964695, "duration": 0.025012969970703125}, {"name": "Required Before Filters", "component": "Timer", "start": **********.989709, "duration": 0.0037310123443603516}, {"name": "Routing", "component": "Timer", "start": **********.993448, "duration": 0.005178928375244141}, {"name": "Before Filters", "component": "Timer", "start": **********.998958, "duration": 0.006143808364868164}, {"name": "Controller", "component": "Timer", "start": **********.005106, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.005107, "duration": 0.0010139942169189453}, {"name": "After Filters", "component": "Timer", "start": **********.094541, "duration": 6.9141387939453125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.094551, "duration": 0.0006718635559082031}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(24 total Queries, 24 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "6.24 ms", "sql": "DELETE <strong>FROM</strong> `artikel`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;12&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2848", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:521", "function": "        CodeIgniter\\Database\\BaseBuilder->delete()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1157", "function": "        CodeIgniter\\Model->doDelete()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Artikel.php:88", "function": "        CodeIgniter\\BaseModel->delete()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Artikel.php:88", "qid": "1e3b4b8ab1fdb57401c84757ad10224c"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>ORDER</strong> <strong>BY</strong> `display_order` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:675", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:27", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:27", "qid": "7f0e6ac917a0e1aa3a88079445d28729"}, {"hover": "", "class": "", "duration": "2.53 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 5\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;18&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "ea4345efec8a3426ef59d8d4226bbcf7"}, {"hover": "", "class": "", "duration": "2.47 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 6\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;13&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "8d886c881ffc398359fc738c6a68cd69"}, {"hover": "", "class": "", "duration": "2.21 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 7\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;19&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "6ceff45527f381eaddfd57b3ae8537e5"}, {"hover": "", "class": "", "duration": "2.36 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 8\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;20&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "7676bb08592b4ddaaa4d5989d5bc5fa6"}, {"hover": "", "class": "", "duration": "2.27 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 9\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;21&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "b55ac76490fd7bed05a23afd04e37770"}, {"hover": "", "class": "", "duration": "2.48 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 10\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;22&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "81355ae3a821ae9c3022280405c69749"}, {"hover": "", "class": "", "duration": "1.86 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 11\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;23&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "b73248bff8eb99f697cf2c7213b93121"}, {"hover": "", "class": "", "duration": "1.75 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 12\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;24&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "27df411953591c530d2eda31a863c4af"}, {"hover": "", "class": "", "duration": "1.57 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 13\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;25&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "dd3b7ebd028f4dbfe97a730a4d60f998"}, {"hover": "", "class": "", "duration": "1.59 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 14\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;26&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "5885706a2130df425f1d91e1ed635415"}, {"hover": "", "class": "", "duration": "1.87 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 15\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;27&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "304e7179a2dbb879dc2e2b3a5475ba3f"}, {"hover": "", "class": "", "duration": "1.63 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 16\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;28&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "160387fcb2a5d9c402a88fdf28dc1508"}, {"hover": "", "class": "", "duration": "1.69 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 17\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;29&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "f194b5715672c8932c9f62caabf5fc9d"}, {"hover": "", "class": "", "duration": "2.05 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 18\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;30&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "1278759f5108d4c39abfda000ddc6d73"}, {"hover": "", "class": "", "duration": "1.88 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 19\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;31&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "8a338f132329d5812167c98e04176de5"}, {"hover": "", "class": "", "duration": "1.7 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 20\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;32&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "ea1db466900101c669cd40705f9fdf3e"}, {"hover": "", "class": "", "duration": "1.72 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 21\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;33&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "01f386faba0a697575cc3bfb612866d6"}, {"hover": "", "class": "", "duration": "1.58 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 22\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;34&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "0280e4ee9105f4c9d5aa7ea4207e7108"}, {"hover": "", "class": "", "duration": "1.66 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 23\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;35&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "1788fb5ce6a94335b2172d1b77375e7d"}, {"hover": "", "class": "", "duration": "1.79 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 24\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;36&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "73f2b7d5f05d2adb08f09d9c74f6bc06"}, {"hover": "", "class": "", "duration": "1.63 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 25\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;37&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "6468e9c596256d7a426a22c61e699b79"}, {"hover": "", "class": "", "duration": "1.65 ms", "sql": "<strong>UPDATE</strong> `artikel` SET `display_order` = 26\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;38&#039;)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2521", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:461", "function": "        CodeIgniter\\Database\\BaseBuilder->update()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1027", "function": "        CodeIgniter\\Model->doUpdate()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:863", "function": "        CodeIgniter\\BaseModel->update()", "index": "  4    "}, {"file": "APPPATH\\Models\\ArtikelModel.php:32", "function": "        CodeIgniter\\Model->update()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\Artikel.php:91", "function": "        App\\Models\\ArtikelModel->reorderDisplayNumbers()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->delete()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\ArtikelModel.php:32", "qid": "83f2284f4e04512df7176d8fc062174e"}]}, "badgeValue": 24, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.017991, "duration": "0.021664"}, {"name": "Query", "component": "Database", "start": **********.040697, "duration": "0.006239", "query": "DELETE <strong>FROM</strong> `artikel`\n<strong>WHERE</strong> `id` <strong>IN</strong> (&#039;12&#039;)"}, {"name": "Query", "component": "Database", "start": **********.047046, "duration": "0.000400", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n<strong>ORDER</strong> <strong>BY</strong> `display_order` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.049125, "duration": "0.002526", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 5\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;18&#039;)"}, {"name": "Query", "component": "Database", "start": **********.051783, "duration": "0.002474", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 6\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;13&#039;)"}, {"name": "Query", "component": "Database", "start": **********.054415, "duration": "0.002209", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 7\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;19&#039;)"}, {"name": "Query", "component": "Database", "start": **********.056759, "duration": "0.002360", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 8\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;20&#039;)"}, {"name": "Query", "component": "Database", "start": **********.059262, "duration": "0.002274", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 9\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;21&#039;)"}, {"name": "Query", "component": "Database", "start": **********.061713, "duration": "0.002475", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 10\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;22&#039;)"}, {"name": "Query", "component": "Database", "start": **********.064308, "duration": "0.001855", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 11\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;23&#039;)"}, {"name": "Query", "component": "Database", "start": **********.066248, "duration": "0.001753", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 12\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;24&#039;)"}, {"name": "Query", "component": "Database", "start": **********.068092, "duration": "0.001572", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 13\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;25&#039;)"}, {"name": "Query", "component": "Database", "start": **********.069752, "duration": "0.001589", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 14\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;26&#039;)"}, {"name": "Query", "component": "Database", "start": **********.071473, "duration": "0.001874", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 15\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;27&#039;)"}, {"name": "Query", "component": "Database", "start": **********.073444, "duration": "0.001625", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 16\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;28&#039;)"}, {"name": "Query", "component": "Database", "start": **********.075216, "duration": "0.001689", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 17\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;29&#039;)"}, {"name": "Query", "component": "Database", "start": **********.07706, "duration": "0.002053", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 18\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;30&#039;)"}, {"name": "Query", "component": "Database", "start": **********.079261, "duration": "0.001883", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 19\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;31&#039;)"}, {"name": "Query", "component": "Database", "start": **********.08128, "duration": "0.001699", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 20\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;32&#039;)"}, {"name": "Query", "component": "Database", "start": **********.083075, "duration": "0.001725", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 21\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;33&#039;)"}, {"name": "Query", "component": "Database", "start": **********.084892, "duration": "0.001576", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 22\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;34&#039;)"}, {"name": "Query", "component": "Database", "start": **********.086591, "duration": "0.001655", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 23\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;35&#039;)"}, {"name": "Query", "component": "Database", "start": **********.08836, "duration": "0.001790", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 24\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;36&#039;)"}, {"name": "Query", "component": "Database", "start": **********.090242, "duration": "0.001630", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 25\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;37&#039;)"}, {"name": "Query", "component": "Database", "start": **********.092024, "duration": "0.001654", "query": "<strong>UPDATE</strong> `artikel` SET `display_order` = 26\n<strong>WHERE</strong> `artikel`.`id` <strong>IN</strong> (&#039;38&#039;)"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 149 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RedirectResponse.php", "name": "RedirectResponse.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Artikel.php", "name": "Artikel.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\Auth.php", "name": "Auth.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 149, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Artikel", "method": "delete", "paramCount": 1, "truePCount": 1, "params": [{"name": "$id = ", "value": "12"}]}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "services", "handler": "\\App\\Controllers\\Page::services"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\artikel::index"}, {"method": "GET", "route": "artikel/(.*)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "user", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 15, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "9.81", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.56", "count": 24}}}, "badgeValue": 25, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.979891, "duration": 0.00981283187866211}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.046943, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.047449, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.051657, "duration": 2.4080276489257812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.054263, "duration": 2.7894973754882812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.056629, "duration": 2.5987625122070312e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.059129, "duration": 2.6941299438476562e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.061542, "duration": 3.886222839355469e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.064192, "duration": 2.193450927734375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.066166, "duration": 1.3113021850585938e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.068004, "duration": 1.4066696166992188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.069667, "duration": 1.4781951904296875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.071345, "duration": 2.09808349609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.07335, "duration": 1.811981201171875e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.075075, "duration": 2.8133392333984375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.076911, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.079119, "duration": 2.9087066650390625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.081149, "duration": 2.7894973754882812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.082982, "duration": 1.7881393432617188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.084804, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.086472, "duration": 2.002716064453125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.08825, "duration": 2.3126602172851562e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.090154, "duration": 1.5974044799804688e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.091878, "duration": 3.1948089599609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.093685, "duration": 3.409385681152344e-05}]}], "vars": {"varData": {"View Data": []}, "session": {"__ci_last_regenerate": "<pre>1750864860</pre>", "_ci_previous_url": "http://localhost:8080/index.php/admin/artikel", "user_id": "4", "user_name": "admin", "user_email": "<EMAIL>", "logged_in": "<pre>1</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/index.php/admin/artikel", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id,en-US;q=0.9,en;q=0.8", "Cookie": "ci_session=4a6190cb3f47411484394394fc11dba4"}, "cookies": {"ci_session": "4a6190cb3f47411484394394fc11dba4"}, "request": "HTTP/1.1", "response": {"statusCode": 302, "reason": "Found", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8", "Location": "http://localhost:8080/index.php/admin/artikel"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}