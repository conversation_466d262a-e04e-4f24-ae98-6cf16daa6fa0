<?php
/**
 * Simple Pagination Template with All Page Numbers
 */

$totalPages = $pager->getPageCount();
$currentPage = $pager->getCurrentPage();
?>

<nav aria-label="Page navigation">
    <div class="pagination">
        <ul>
            <?php if ($pager->hasPrevious()) : ?>
                <li>
                    <a href="<?= $pager->getPrevious() ?>" aria-label="Previous page" rel="prev">
                        Previous
                    </a>
                </li>
            <?php endif ?>

            <?php for ($i = 1; $i <= $totalPages; $i++) : ?>
                <li <?= ($i == $currentPage) ? 'class="active"' : '' ?>>
                    <?php if ($i == $currentPage) : ?>
                        <span aria-current="page"><?= $i ?></span>
                    <?php else : ?>
                        <a href="<?= $pager->getPageURI($i) ?>" aria-label="Go to page <?= $i ?>"><?= $i ?></a>
                    <?php endif ?>
                </li>
            <?php endfor; ?>

            <?php if ($pager->hasNext()) : ?>
                <li>
                    <a href="<?= $pager->getNext() ?>" aria-label="Next page" rel="next">
                        Next
                    </a>
                </li>
            <?php endif ?>
        </ul>
    </div>
</nav>
