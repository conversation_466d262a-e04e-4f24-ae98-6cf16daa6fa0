<?php
/**
 * Bulletproof Pagination Template
 * Forces display of all pages regardless of CodeIgniter's internal logic
 */

// Get pagination details
$currentPage = $pager->getCurrentPage();
$totalPages = $pager->getPageCount();
$total = $pager->getTotal();
$perPage = $pager->getPerPage();

// Calculate pages manually if needed
if ($totalPages <= 1 && $total > $perPage) {
    $totalPages = ceil($total / $perPage);
}

// Get base URL for pagination
$baseUrl = current_url();
$queryString = $_SERVER['QUERY_STRING'] ?? '';
parse_str($queryString, $params);
unset($params['page']); // Remove existing page parameter
$baseQuery = http_build_query($params);
$baseUrl = $baseUrl . ($baseQuery ? '?' . $baseQuery : '');
$separator = $baseQuery ? '&' : '?';
?>

<nav aria-label="Page navigation" class="simple-pagination">
    <div class="page-numbers">
        <?php if ($totalPages > 1) : ?>
            <?php for ($i = 1; $i <= $totalPages; $i++) : ?>
                <?php if ($i == $currentPage) : ?>
                    <span class="page-num active" aria-current="page"><?= $i ?></span>
                <?php else : ?>
                    <a href="<?= $baseUrl . $separator . 'page=' . $i ?>" class="page-num" aria-label="Go to page <?= $i ?>"><?= $i ?></a>
                <?php endif ?>
            <?php endfor ?>
        <?php else : ?>
            <!-- Fallback: Use CodeIgniter's built-in links -->
            <?php $pager->setSurroundCount(10); ?>
            <?php foreach ($pager->links() as $link) : ?>
                <?php if ($link['active']) : ?>
                    <span class="page-num active" aria-current="page"><?= $link['title'] ?></span>
                <?php else : ?>
                    <a href="<?= $link['uri'] ?>" class="page-num" aria-label="Go to page <?= $link['title'] ?>"><?= $link['title'] ?></a>
                <?php endif ?>
            <?php endforeach ?>
        <?php endif ?>
    </div>
</nav>

<style>
/* Minimalist Pagination Styles */
.simple-pagination {
    display: flex;
    justify-content: center;
    margin: 24px 0;
}

.page-numbers {
    display: flex;
    gap: 12px;
    align-items: center;
}

.page-num {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ffffff;
    color: #6c757d;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.15s ease;
    border: 1px solid #e9ecef;
    cursor: pointer;
}

.page-num:hover {
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-color: #dee2e6;
}

.page-num.active {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
    cursor: default;
}

.page-num.active:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .page-numbers {
        gap: 10px;
    }

    .page-num {
        width: 38px;
        height: 38px;
        font-size: 14px;
    }
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .simple-pagination {
        margin: 20px 0;
    }

    .page-numbers {
        gap: 8px;
    }

    .page-num {
        width: 36px;
        height: 36px;
        font-size: 13px;
    }
}
</style>
