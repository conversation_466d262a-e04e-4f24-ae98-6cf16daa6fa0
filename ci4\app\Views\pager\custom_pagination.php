<?php
/**
 * Ultra Simple Pagination - Force Show All Pages
 */

$currentPage = $pager->getCurrentPage();
$total = $pager->getTotal();
$perPage = $pager->getPerPage();

// Always calculate pages manually
$totalPages = ceil($total / $perPage);

// Get current URL
$currentUrl = current_url();
$queryParams = $_GET ?? [];
unset($queryParams['page']);
$queryString = http_build_query($queryParams);
$baseUrl = $currentUrl . ($queryString ? '?' . $queryString : '');
$separator = $queryString ? '&' : '?';
?>

<nav aria-label="Page navigation" class="simple-pagination">
    <div class="page-numbers">
        <?php if ($totalPages > 1) : ?>
            <?php for ($i = 1; $i <= $totalPages; $i++) : ?>
                <?php if ($i == $currentPage) : ?>
                    <span class="page-num active" aria-current="page"><?= $i ?></span>
                <?php else : ?>
                    <a href="<?= $baseUrl . $separator . 'page=' . $i ?>" class="page-num"><?= $i ?></a>
                <?php endif ?>
            <?php endfor ?>
        <?php else : ?>
            <span class="page-num active" aria-current="page">1</span>
        <?php endif ?>
    </div>
</nav>

<style>
/* Minimalist Pagination Styles */
.simple-pagination {
    display: flex;
    justify-content: center;
    margin: 24px 0;
}

.page-numbers {
    display: flex;
    gap: 12px;
    align-items: center;
}

.page-num {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ffffff;
    color: #6c757d;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.15s ease;
    border: 1px solid #e9ecef;
    cursor: pointer;
}

.page-num:hover {
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-color: #dee2e6;
}

.page-num.active {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
    cursor: default;
}

.page-num.active:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .page-numbers {
        gap: 10px;
    }

    .page-num {
        width: 38px;
        height: 38px;
        font-size: 14px;
    }
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .simple-pagination {
        margin: 20px 0;
    }

    .page-numbers {
        gap: 8px;
    }

    .page-num {
        width: 36px;
        height: 36px;
        font-size: 13px;
    }
}
</style>
