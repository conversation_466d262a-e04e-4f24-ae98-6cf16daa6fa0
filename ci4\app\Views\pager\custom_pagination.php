<?php
/**
 * Custom Pagination Template - Force Show All Pages
 */

// Get pagination details
$currentPage = $pager->getCurrentPage();
$totalPages = $pager->getPageCount();
$perPage = $pager->getPerPage();
$total = $pager->getTotal();

// Debug info (remove in production)
// echo "<!-- Debug: Current: $currentPage, Total Pages: $totalPages, Per Page: $perPage, Total: $total -->";
?>

<nav aria-label="Page navigation">
    <div class="pagination">
        <ul>
            <?php if ($currentPage > 1) : ?>
                <li>
                    <a href="<?= $pager->getPrevious() ?>" aria-label="Previous page" rel="prev">
                        Previous
                    </a>
                </li>
            <?php endif ?>

            <?php
            // Force show all page numbers manually
            for ($i = 1; $i <= $totalPages; $i++) :
                $pageUri = $pager->getPageURI($i);
            ?>
                <li <?= ($i == $currentPage) ? 'class="active"' : '' ?>>
                    <?php if ($i == $currentPage) : ?>
                        <span aria-current="page"><?= $i ?></span>
                    <?php else : ?>
                        <a href="<?= $pageUri ?>" aria-label="Go to page <?= $i ?>"><?= $i ?></a>
                    <?php endif ?>
                </li>
            <?php endfor; ?>

            <?php if ($currentPage < $totalPages) : ?>
                <li>
                    <a href="<?= $pager->getNext() ?>" aria-label="Next page" rel="next">
                        Next
                    </a>
                </li>
            <?php endif ?>
        </ul>
    </div>
</nav>
