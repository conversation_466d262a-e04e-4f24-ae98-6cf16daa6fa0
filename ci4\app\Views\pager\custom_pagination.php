<?php
/**
 * Simplified Clean Pagination Template
 * Responsive design with modern card-based buttons
 */

// Set surround count to show all pages for small pagination
$pager->setSurroundCount(10);
?>

<nav aria-label="Page navigation" class="pagination-nav">
    <div class="pagination-wrapper">
        <?php foreach ($pager->links() as $link) : ?>
            <?php if ($link['active']) : ?>
                <span class="page-btn active" aria-current="page"><?= $link['title'] ?></span>
            <?php else : ?>
                <a href="<?= $link['uri'] ?>" class="page-btn" aria-label="Go to page <?= $link['title'] ?>"><?= $link['title'] ?></a>
            <?php endif ?>
        <?php endforeach ?>
    </div>
</nav>

<style>
/* Simplified Clean Pagination Styles */
.pagination-nav {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.pagination-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
}

.page-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
}

.page-btn:hover {
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-btn.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
    cursor: default;
}

.page-btn.active:hover {
    background: #007bff;
    transform: none;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .pagination-wrapper {
        gap: 6px;
    }

    .page-btn {
        min-width: 36px;
        height: 36px;
        padding: 6px 10px;
        font-size: 13px;
    }
}
</style>
