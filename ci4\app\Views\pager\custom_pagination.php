<?php
/**
 * Minimalist Clean Pagination Template
 * Simplified design with maintained functionality and full responsiveness
 */

// Set surround count to show all pages for small pagination
$pager->setSurroundCount(10);
?>

<nav aria-label="Page navigation" class="simple-pagination">
    <div class="page-numbers">
        <?php foreach ($pager->links() as $link) : ?>
            <?php if ($link['active']) : ?>
                <span class="page-num active" aria-current="page"><?= $link['title'] ?></span>
            <?php else : ?>
                <a href="<?= $link['uri'] ?>" class="page-num" aria-label="Go to page <?= $link['title'] ?>"><?= $link['title'] ?></a>
            <?php endif ?>
        <?php endforeach ?>
    </div>
</nav>

<style>
/* Minimalist Pagination Styles */
.simple-pagination {
    display: flex;
    justify-content: center;
    margin: 24px 0;
}

.page-numbers {
    display: flex;
    gap: 12px;
    align-items: center;
}

.page-num {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ffffff;
    color: #6c757d;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.15s ease;
    border: 1px solid #e9ecef;
    cursor: pointer;
}

.page-num:hover {
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-color: #dee2e6;
}

.page-num.active {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
    cursor: default;
}

.page-num.active:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .page-numbers {
        gap: 10px;
    }

    .page-num {
        width: 38px;
        height: 38px;
        font-size: 14px;
    }
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .simple-pagination {
        margin: 20px 0;
    }

    .page-numbers {
        gap: 8px;
    }

    .page-num {
        width: 36px;
        height: 36px;
        font-size: 13px;
    }
}
</style>
