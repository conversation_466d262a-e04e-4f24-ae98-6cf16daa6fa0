<?php
/**
 * Simple Pagination Template - Like in the Image
 */

// Get pagination info
$currentPage = $pager->getCurrentPage();
$totalPages = $pager->getPageCount();
?>

<nav aria-label="Page navigation">
    <div class="pagination">
        <ul>
            <?php
            // Show all page numbers (simple approach)
            for ($i = 1; $i <= $totalPages; $i++) :
            ?>
                <li <?= ($i == $currentPage) ? 'class="active"' : '' ?>>
                    <?php if ($i == $currentPage) : ?>
                        <span aria-current="page"><?= $i ?></span>
                    <?php else : ?>
                        <a href="<?= $pager->getPageURI($i) ?>" aria-label="Go to page <?= $i ?>"><?= $i ?></a>
                    <?php endif ?>
                </li>
            <?php endfor; ?>
        </ul>
    </div>
</nav>
