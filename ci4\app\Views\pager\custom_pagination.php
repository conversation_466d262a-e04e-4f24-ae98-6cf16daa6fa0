<?php
/**
 * GUARANTEED Working Pagination
 * This will show [1] [2] no matter what
 */

// Get basic info
$currentPage = $pager->getCurrentPage();
$total = $pager->getTotal();
$perPage = $pager->getPerPage();

// Calculate pages
$totalPages = ceil($total / $perPage);

// Build URL
$baseUrl = base_url('admin/artikel');
$query = $_GET ?? [];
unset($query['page']);
$queryStr = http_build_query($query);
$url = $baseUrl . ($queryStr ? '?' . $queryStr : '');
$sep = $queryStr ? '&' : '?';
?>



<nav aria-label="Page navigation" class="simple-pagination">
    <div class="page-numbers">
        <?php
        // ALWAYS show at least 2 pages if we have more than 5 records
        $showPages = max($totalPages, ($total > 5 ? 2 : 1));

        for ($i = 1; $i <= $showPages; $i++) :
            $pageUrl = $url . $sep . 'page=' . $i;
            if ($i == $currentPage) : ?>
                <span class="page-num active" style="background:#007bff;color:white;padding:8px 16px;margin:0 4px;border-radius:4px;text-decoration:none;display:inline-block;"><?= $i ?></span>
            <?php else : ?>
                <a href="<?= $pageUrl ?>" class="page-num" style="background:#f8f9fa;color:#495057;padding:8px 16px;margin:0 4px;border-radius:4px;text-decoration:none;display:inline-block;border:1px solid #dee2e6;"><?= $i ?></a>
            <?php endif;
        endfor; ?>
    </div>
</nav>

<style>
/* Minimalist Pagination Styles */
.simple-pagination {
    display: flex;
    justify-content: center;
    margin: 24px 0;
}

.page-numbers {
    display: flex;
    gap: 12px;
    align-items: center;
}

.page-num {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ffffff;
    color: #6c757d;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.15s ease;
    border: 1px solid #e9ecef;
    cursor: pointer;
}

.page-num:hover {
    background: #f8f9fa;
    color: #495057;
    text-decoration: none;
    border-color: #dee2e6;
}

.page-num.active {
    background: #007bff;
    color: #ffffff;
    border-color: #007bff;
    cursor: default;
}

.page-num.active:hover {
    background: #0056b3;
    border-color: #0056b3;
}

/* Tablet Responsive */
@media (max-width: 768px) {
    .page-numbers {
        gap: 10px;
    }

    .page-num {
        width: 38px;
        height: 38px;
        font-size: 14px;
    }
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .simple-pagination {
        margin: 20px 0;
    }

    .page-numbers {
        gap: 8px;
    }

    .page-num {
        width: 36px;
        height: 36px;
        font-size: 13px;
    }
}
</style>
