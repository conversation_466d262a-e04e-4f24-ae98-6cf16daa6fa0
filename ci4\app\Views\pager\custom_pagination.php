<?php
/**
 * Custom Pagination Template
 */

$pager->setSurroundCount(2);
?>

<nav aria-label="Page navigation">
    <div class="pagination">
        <ul>
            <?php if ($pager->hasPrevious()) : ?>
                <li>
                    <a href="<?= $pager->getFirst() ?>" aria-label="First">
                        <span>First</span>
                    </a>
                </li>
                <li>
                    <a href="<?= $pager->getPrevious() ?>" aria-label="Previous" rel="prev">
                        <span>Previous</span>
                    </a>
                </li>
            <?php endif ?>

            <?php foreach ($pager->links() as $link) : ?>
                <li <?= $link['active'] ? 'class="active"' : '' ?>>
                    <?php if ($link['active']) : ?>
                        <span><?= $link['title'] ?></span>
                    <?php else : ?>
                        <a href="<?= $link['uri'] ?>"><?= $link['title'] ?></a>
                    <?php endif ?>
                </li>
            <?php endforeach ?>

            <?php if ($pager->hasNext()) : ?>
                <li>
                    <a href="<?= $pager->getNext() ?>" aria-label="Next" rel="next">
                        <span>Next</span>
                    </a>
                </li>
                <li>
                    <a href="<?= $pager->getLast() ?>" aria-label="Last">
                        <span>Last</span>
                    </a>
                </li>
            <?php endif ?>
        </ul>
    </div>
</nav>
