<?php
/**
 * Very Simple Pagination Template - Show All Pages
 */

// Calculate pagination manually
$currentPage = $pager->getCurrentPage();
$totalRecords = $pager->getTotal();
$perPage = $pager->getPerPage();
$totalPages = ceil($totalRecords / $perPage);

// Get base URL
$baseUrl = current_url();
$queryString = $_SERVER['QUERY_STRING'] ?? '';
parse_str($queryString, $params);
unset($params['page']); // Remove page parameter
$baseQuery = http_build_query($params);
?>

<nav aria-label="Page navigation">
    <div class="pagination">
        <ul>
            <?php if ($currentPage > 1) : ?>
                <li>
                    <a href="<?= $baseUrl . ($baseQuery ? '?' . $baseQuery . '&page=' . ($currentPage - 1) : '?page=' . ($currentPage - 1)) ?>" aria-label="Previous page" rel="prev">
                        Previous
                    </a>
                </li>
            <?php endif ?>

            <?php for ($i = 1; $i <= $totalPages; $i++) : ?>
                <li <?= ($i == $currentPage) ? 'class="active"' : '' ?>>
                    <?php if ($i == $currentPage) : ?>
                        <span aria-current="page"><?= $i ?></span>
                    <?php else : ?>
                        <a href="<?= $baseUrl . ($baseQuery ? '?' . $baseQuery . '&page=' . $i : '?page=' . $i) ?>" aria-label="Go to page <?= $i ?>"><?= $i ?></a>
                    <?php endif ?>
                </li>
            <?php endfor; ?>

            <?php if ($currentPage < $totalPages) : ?>
                <li>
                    <a href="<?= $baseUrl . ($baseQuery ? '?' . $baseQuery . '&page=' . ($currentPage + 1) : '?page=' . ($currentPage + 1)) ?>" aria-label="Next page" rel="next">
                        Next
                    </a>
                </li>
            <?php endif ?>
        </ul>
    </div>
</nav>
