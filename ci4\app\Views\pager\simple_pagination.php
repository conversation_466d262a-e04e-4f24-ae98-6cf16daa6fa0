<?php
/**
 * Working Pagination Template - Uses CodeIgniter 4 Pager
 */

// Set surround count to show all pages for small pagination
$pager->setSurroundCount(10);
?>

<nav aria-label="Page navigation">
    <div class="pagination">
        <ul>
            <?php if ($pager->hasPrevious()) : ?>
                <li>
                    <a href="<?= $pager->getPrevious() ?>" aria-label="Previous page" rel="prev">
                        Previous
                    </a>
                </li>
            <?php endif ?>

            <?php foreach ($pager->links() as $link) : ?>
                <li <?= $link['active'] ? 'class="active"' : '' ?>>
                    <?php if ($link['active']) : ?>
                        <span aria-current="page"><?= $link['title'] ?></span>
                    <?php else : ?>
                        <a href="<?= $link['uri'] ?>" aria-label="Go to page <?= $link['title'] ?>"><?= $link['title'] ?></a>
                    <?php endif ?>
                </li>
            <?php endforeach ?>

            <?php if ($pager->hasNext()) : ?>
                <li>
                    <a href="<?= $pager->getNext() ?>" aria-label="Next page" rel="next">
                        Next
                    </a>
                </li>
            <?php endif ?>
        </ul>
    </div>
</nav>
