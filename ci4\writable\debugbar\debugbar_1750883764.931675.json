{"url": "http://localhost:8080/index.php/admin/artikel", "method": "GET", "isAJAX": false, "startTime": **********.690008, "totalTime": 115.4, "totalMemory": "5.601", "segmentDuration": 20, "segmentCount": 6, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.700121, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.743454, "duration": 0.005891084671020508}, {"name": "Routing", "component": "Timer", "start": **********.749355, "duration": 0.008406877517700195}, {"name": "Before Filters", "component": "Timer", "start": **********.758225, "duration": 0.009293079376220703}, {"name": "Controller", "component": "Timer", "start": **********.767524, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.767527, "duration": 0.001786947250366211}, {"name": "After Filters", "component": "Timer", "start": **********.804633, "duration": 1.0013580322265625e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.80468, "duration": 0.0007638931274414062}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(2 total Queries, 2 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `artikel`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:1283", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\Artikel.php:40", "function": "        CodeIgniter\\BaseModel->paginate()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->admin_index()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH\\Controllers\\Artikel.php:40", "qid": "44e496158e120097ee78dd3fa1e4c327"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n <strong>LIMIT</strong> 10, 10", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:675", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "SYSTEMPATH\\BaseModel.php:1287", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\Artikel.php:40", "function": "        CodeIgniter\\BaseModel->paginate()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\Artikel->admin_index()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\Artikel.php:40", "qid": "f13434479f73da995c9e63316660dcc4"}]}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.790701, "duration": "0.001631"}, {"name": "Query", "component": "Database", "start": **********.793466, "duration": "0.000306", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `artikel`"}, {"name": "Query", "component": "Database", "start": **********.795545, "duration": "0.000407", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `artikel`\n <strong>LIMIT</strong> 10, 10"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 3, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: template/admin_header.php", "component": "Views", "start": **********.796715, "duration": 0.000926971435546875}, {"name": "View: template/admin_footer.php", "component": "Views", "start": **********.804016, "duration": 0.00026679039001464844}, {"name": "View: artikel/admin_index.php", "component": "Views", "start": **********.796059, "duration": 0.008382081985473633}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 158 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Language\\Language.php", "name": "Language.php"}, {"path": "SYSTEMPATH\\Language\\en\\Pager.php", "name": "Pager.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Pager\\Pager.php", "name": "Pager.php"}, {"path": "SYSTEMPATH\\Pager\\PagerInterface.php", "name": "PagerInterface.php"}, {"path": "SYSTEMPATH\\Pager\\PagerRenderer.php", "name": "PagerRenderer.php"}, {"path": "SYSTEMPATH\\Pager\\Views\\default_full.php", "name": "default_full.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Pager.php", "name": "Pager.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\Artikel.php", "name": "Artikel.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Filters\\Auth.php", "name": "Auth.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "APPPATH\\Views\\artikel\\admin_index.php", "name": "admin_index.php"}, {"path": "APPPATH\\Views\\template\\admin_footer.php", "name": "admin_footer.php"}, {"path": "APPPATH\\Views\\template\\admin_header.php", "name": "admin_header.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 158, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Artikel", "method": "admin_index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "services", "handler": "\\App\\Controllers\\Page::services"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\artikel::index"}, {"method": "GET", "route": "artikel/(.*)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "user", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "datacheck", "handler": "\\App\\Controllers\\DataCheck::index"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 16, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "14.68", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.04", "count": 2}}}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.728767, "duration": 0.014678001403808594}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.793777, "duration": 2.4080276489257812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.795956, "duration": 2.002716064453125e-05}]}], "vars": {"varData": {"View Data": {"title": "Daftar Artikel", "q": "", "artikel": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (10)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (10)</li><li>Contents (10)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>id</th><th>display_order</th><th>judul</th><th>isi</th><th>gambar</th><th>status</th><th>slug</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">24</td><td title=\"string (2)\">11</td><td title=\"string (17)\">Artikel Sample 11</td><td title=\"string (271)\">Ini adalah isi artikel sample nomor 11. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-11</td></tr><tr><th>1</th><td title=\"string (2)\">25</td><td title=\"string (2)\">12</td><td title=\"string (17)\">Artikel Sample 12</td><td title=\"string (271)\">Ini adalah isi artikel sample nomor 12. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-12</td></tr><tr><th>2</th><td title=\"string (2)\">54</td><td title=\"string (2)\">13</td><td title=\"string (17)\">Artikel Sample 13</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 13. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-13</td></tr><tr><th>3</th><td title=\"string (2)\">55</td><td title=\"string (2)\">14</td><td title=\"string (17)\">Artikel Sample 14</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 14. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-14</td></tr><tr><th>4</th><td title=\"string (2)\">56</td><td title=\"string (2)\">15</td><td title=\"string (17)\">Artikel Sample 15</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 15. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-15</td></tr><tr><th>5</th><td title=\"string (2)\">57</td><td title=\"string (2)\">16</td><td title=\"string (17)\">Artikel Sample 16</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 16. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-16</td></tr><tr><th>6</th><td title=\"string (2)\">58</td><td title=\"string (2)\">17</td><td title=\"string (17)\">Artikel Sample 17</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 17. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-17</td></tr><tr><th>7</th><td title=\"string (2)\">59</td><td title=\"string (2)\">18</td><td title=\"string (17)\">Artikel Sample 18</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 18. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-18</td></tr><tr><th>8</th><td title=\"string (2)\">60</td><td title=\"string (2)\">19</td><td title=\"string (17)\">Artikel Sample 19</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 19. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-19</td></tr><tr><th>9</th><td title=\"string (2)\">61</td><td title=\"string (2)\">20</td><td title=\"string (17)\">Artikel Sample 20</td><td title=\"string (163)\">Ini adalah isi artikel sample nomor 20. Lorem ipsum dolor sit amet, consectUTF-8</td><td title=\"string (0)\"></td><td title=\"string (1)\">1</td><td title=\"string (17)\">artikel-sample-20</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[0]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[0]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 11\"<div class=\"access-path\">$value[0]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (271) \"Ini adalah isi artikel sample nomor 11. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[0]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 11. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-11\"<div class=\"access-path\">$value[0]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[1]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[1]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 12\"<div class=\"access-path\">$value[1]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (271) \"Ini adalah isi artikel sample nomor 12. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[1]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 12. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-12\"<div class=\"access-path\">$value[1]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"54\"<div class=\"access-path\">$value[2]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[2]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 13\"<div class=\"access-path\">$value[2]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 13. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[2]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 13. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-13\"<div class=\"access-path\">$value[2]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"55\"<div class=\"access-path\">$value[3]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[3]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 14\"<div class=\"access-path\">$value[3]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 14. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[3]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 14. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[3]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-14\"<div class=\"access-path\">$value[3]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"56\"<div class=\"access-path\">$value[4]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[4]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 15\"<div class=\"access-path\">$value[4]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 15. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[4]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 15. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[4]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-15\"<div class=\"access-path\">$value[4]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"57\"<div class=\"access-path\">$value[5]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[5]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 16\"<div class=\"access-path\">$value[5]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 16. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[5]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 16. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[5]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-16\"<div class=\"access-path\">$value[5]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[6]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[6]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 17\"<div class=\"access-path\">$value[6]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 17. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[6]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 17. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[6]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-17\"<div class=\"access-path\">$value[6]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"59\"<div class=\"access-path\">$value[7]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[7]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 18\"<div class=\"access-path\">$value[7]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 18. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[7]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 18. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[7]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-18\"<div class=\"access-path\">$value[7]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"60\"<div class=\"access-path\">$value[8]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[8]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 19\"<div class=\"access-path\">$value[8]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 19. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[8]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 19. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[8]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-19\"<div class=\"access-path\">$value[8]['slug']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"61\"<div class=\"access-path\">$value[9]['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>display_order</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[9]['display_order']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>judul</dfn> =&gt; <var>string</var> (17) \"Artikel Sample 20\"<div class=\"access-path\">$value[9]['judul']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>isi</dfn> =&gt; <var>string</var> (163) \"Ini adalah isi artikel sample nomor 20. Lorem ipsum dolor sit amet, consecte...<div class=\"access-path\">$value[9]['isi']</div></dt><dd><pre>Ini adalah isi artikel sample nomor 20. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gambar</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[9]['gambar']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>slug</dfn> =&gt; <var>string</var> (17) \"artikel-sample-20\"<div class=\"access-path\">$value[9]['slug']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "pager": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>CodeIgniter\\Pager\\Pager</var>#88 (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (5)</li><li>Methods (22)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>groups</dfn> -&gt; <var>array</var> (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (2)</li><li>Contents (2)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>currentUri</th><th>uri</th><th>hasMore</th><th>total</th><th>perPage</th><th>pageCount</th><th>pageSelector</th><th>currentPage</th></tr></thead><tbody><tr><th>default</th><td title=\"CodeIgniter\\HTTP\\SiteURI (20)\"><var>CodeIgniter\\HTTP\\SiteURI</var> (20)</td><td title=\"CodeIgniter\\HTTP\\SiteURI (20)\"><var>CodeIgniter\\HTTP\\SiteURI</var> (20)</td><td title=\"boolean\"><var>false</var></td><td title=\"integer\">25</td><td title=\"integer\">10</td><td title=\"integer\">3</td><td title=\"string (4)\">page</td><td title=\"integer\">2</td></tr><tr><th>custom_pagination</th><td title=\"CodeIgniter\\HTTP\\SiteURI (20)\"><var>CodeIgniter\\HTTP\\SiteURI</var> (20)</td><td title=\"CodeIgniter\\HTTP\\SiteURI (20)\"><var>CodeIgniter\\HTTP\\SiteURI</var> (20)</td><td title=\"boolean\"><var>false</var></td><td title=\"null\"><var>null</var></td><td title=\"integer\">20</td><td title=\"integer\">1</td><td title=\"string (22)\">page_custom_pagination</td><td title=\"integer\">1</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>default</dfn> =&gt; <var>array</var> (8)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>currentUri</dfn> =&gt; <var>CodeIgniter\\HTTP\\SiteURI</var>#89 (20)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (20)</li><li>Methods (51)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (5) \"admin\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"artikel\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (24) \"/index.php/admin/artikel\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>page</dfn> =&gt; <var>string</var> (1) \"2\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>baseURL</dfn> -&gt; <var>CodeIgniter\\HTTP\\URI</var>#24 (15)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (15)</li><li>Methods (40)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?string $uri = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param string|null $uri The URI to parse.\n *\n * @throws HTTPException\n *\n * @TODO null for param $uri should be removed.\n *      See https://www.php-fig.org/psr/psr-17/#26-urifactoryinterface\n */\n\n<small>Defined in .../HTTP/URI.php:256</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn>: <var>URI</var> Sets and overwrites any current URI information.</dt><dd><pre>/**\n * Sets and overwrites any current URI information.\n *\n * @return URI\n *\n * @throws HTTPException\n *\n * @deprecated 4.4.0 This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:302</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n *\n * Warning: For backwards-compatability this method\n * assumes URIs with the same host as baseURL should\n * be relative to the project's configuration.\n * This aspect of __toString() is deprecated and should be avoided.\n */\n\n<small>Defined in .../HTTP/URI.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the path portion of the URI.</dt><dd><pre>/**\n * Sets the path portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPath($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:816</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var> Sets the current baseURL.</dt><dd><pre>/**\n * Sets the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:834</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the current baseURL.</dt><dd><pre>/**\n * Returns the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:846</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:862</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url call.</dt><dd><pre>/**\n * Saves our parts from a parse_url call.\n *\n * @return void\n */\n\n<small>Defined in .../HTTP/URI.php:1036</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>baseURL</dfn> <var>string</var> (22) \"http://localhost:8080/\"</dt></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>basePathWithoutIndexPage</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>private readonly</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (9) \"index.php\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>baseSegments</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"index.php\"</dt></dl></dd></dl><dl><dt><var>private</var> <dfn>routePath</dfn> -&gt; <var>string</var> (13) \"admin/artikel\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\App $configApp, string $relativePath = '', ?string $host = null, ?string $scheme = null)</dfn></dt><dd><pre>/**\n * @param         string              $relativePath URI path relative to baseURL. May include\n *                                                  queries or fragments.\n * @param         string|null         $host         Optional current hostname.\n * @param         string|null         $scheme       Optional scheme. 'http' or 'https'.\n * @phpstan-param 'http'|'https'|null $scheme\n */\n\n<small>Defined in .../HTTP/SiteURI.php:94</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>parseRelativePath(string $relativePath)</dfn>: <var>array</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>determineBaseURL(Config\\App $configApp, ?string $host, ?string $scheme)</dfn>: <var>CodeIgniter\\HTTP\\URI</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getIndexPageRoutePath(string $routePath)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>normalizeBaseURL(Config\\App $configApp)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setBasePath()</dfn>: <var>void</var> Sets basePathWithoutIndexPage and baseSegments.</dt><dd><pre>/**\n * Sets basePathWithoutIndexPage and baseSegments.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the baseURL.</dt><dd><pre>/**\n * Returns the baseURL.\n *\n * @interal\n */\n\n<small>Defined in .../HTTP/SiteURI.php:244</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRoutePath()</dfn>: <var>string</var> Returns the URI path relative to baseURL.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL.\n *\n * @return string The Route path.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:262</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/SiteURI.php:278</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setRoutePath(string $routePath)</dfn>: <var>void</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n */\n\n<small>Defined in .../HTTP/SiteURI.php:288</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>convertToSegments(string $path)</dfn>: <var>array</var> Converts path to segments</dt><dd><pre>/**\n * Converts path to segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:304</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url() call.</dt><dd><pre>/**\n * Saves our parts from a parse_url() call.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:335</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>baseUrl($relativePath = '', ?string $scheme = null)</dfn>: <var>string</var> For base_url() helper.</dt><dd><pre>/**\n * For base_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>stringifyRelativePath($relativePath)</dfn>: <var>string</var></dt><dd><pre>/**\n * @param array|string $relativePath URI string or array of URI segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:401</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>siteUrl($relativePath = '', ?string $scheme = null, ?Config\\App $config = null)</dfn>: <var>string</var> For site_url() helper.</dt><dd><pre>/**\n * For site_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n * @param App|null     $config       Alternate configuration to use.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:419</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>currentUri</dfn> <var>string</var> (52) \"http://localhost:8080/index.php/admin/artikel?page=2\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>uri</dfn> =&gt; <var>CodeIgniter\\HTTP\\SiteURI</var>#90 (20)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (20)</li><li>Methods (51)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (5) \"admin\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"artikel\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (24) \"/index.php/admin/artikel\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>page</dfn> =&gt; <var>string</var> (1) \"2\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>baseURL</dfn> -&gt; <var>CodeIgniter\\HTTP\\URI</var>#24 (15)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (15)</li><li>Methods (40)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?string $uri = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param string|null $uri The URI to parse.\n *\n * @throws HTTPException\n *\n * @TODO null for param $uri should be removed.\n *      See https://www.php-fig.org/psr/psr-17/#26-urifactoryinterface\n */\n\n<small>Defined in .../HTTP/URI.php:256</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn>: <var>URI</var> Sets and overwrites any current URI information.</dt><dd><pre>/**\n * Sets and overwrites any current URI information.\n *\n * @return URI\n *\n * @throws HTTPException\n *\n * @deprecated 4.4.0 This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:302</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n *\n * Warning: For backwards-compatability this method\n * assumes URIs with the same host as baseURL should\n * be relative to the project's configuration.\n * This aspect of __toString() is deprecated and should be avoided.\n */\n\n<small>Defined in .../HTTP/URI.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the path portion of the URI.</dt><dd><pre>/**\n * Sets the path portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPath($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:816</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var> Sets the current baseURL.</dt><dd><pre>/**\n * Sets the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:834</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the current baseURL.</dt><dd><pre>/**\n * Returns the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:846</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:862</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url call.</dt><dd><pre>/**\n * Saves our parts from a parse_url call.\n *\n * @return void\n */\n\n<small>Defined in .../HTTP/URI.php:1036</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>baseURL</dfn> <var>string</var> (22) \"http://localhost:8080/\"</dt></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>basePathWithoutIndexPage</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>private readonly</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (9) \"index.php\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>baseSegments</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"index.php\"</dt></dl></dd></dl><dl><dt><var>private</var> <dfn>routePath</dfn> -&gt; <var>string</var> (13) \"admin/artikel\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\App $configApp, string $relativePath = '', ?string $host = null, ?string $scheme = null)</dfn></dt><dd><pre>/**\n * @param         string              $relativePath URI path relative to baseURL. May include\n *                                                  queries or fragments.\n * @param         string|null         $host         Optional current hostname.\n * @param         string|null         $scheme       Optional scheme. 'http' or 'https'.\n * @phpstan-param 'http'|'https'|null $scheme\n */\n\n<small>Defined in .../HTTP/SiteURI.php:94</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>parseRelativePath(string $relativePath)</dfn>: <var>array</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>determineBaseURL(Config\\App $configApp, ?string $host, ?string $scheme)</dfn>: <var>CodeIgniter\\HTTP\\URI</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getIndexPageRoutePath(string $routePath)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>normalizeBaseURL(Config\\App $configApp)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setBasePath()</dfn>: <var>void</var> Sets basePathWithoutIndexPage and baseSegments.</dt><dd><pre>/**\n * Sets basePathWithoutIndexPage and baseSegments.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the baseURL.</dt><dd><pre>/**\n * Returns the baseURL.\n *\n * @interal\n */\n\n<small>Defined in .../HTTP/SiteURI.php:244</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRoutePath()</dfn>: <var>string</var> Returns the URI path relative to baseURL.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL.\n *\n * @return string The Route path.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:262</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/SiteURI.php:278</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setRoutePath(string $routePath)</dfn>: <var>void</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n */\n\n<small>Defined in .../HTTP/SiteURI.php:288</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>convertToSegments(string $path)</dfn>: <var>array</var> Converts path to segments</dt><dd><pre>/**\n * Converts path to segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:304</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url() call.</dt><dd><pre>/**\n * Saves our parts from a parse_url() call.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:335</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>baseUrl($relativePath = '', ?string $scheme = null)</dfn>: <var>string</var> For base_url() helper.</dt><dd><pre>/**\n * For base_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>stringifyRelativePath($relativePath)</dfn>: <var>string</var></dt><dd><pre>/**\n * @param array|string $relativePath URI string or array of URI segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:401</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>siteUrl($relativePath = '', ?string $scheme = null, ?Config\\App $config = null)</dfn>: <var>string</var> For site_url() helper.</dt><dd><pre>/**\n * For site_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n * @param App|null     $config       Alternate configuration to use.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:419</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>uri</dfn> <var>string</var> (52) \"http://localhost:8080/index.php/admin/artikel?page=2\"</dt></dl></li></ul></dd></dl><dl><dt><dfn>hasMore</dfn> =&gt; <var>boolean</var> false</dt></dl><dl><dt><dfn>total</dfn> =&gt; <var>integer</var> 25</dt></dl><dl><dt><dfn>perPage</dfn> =&gt; <var>integer</var> 10</dt></dl><dl><dt><dfn>pageCount</dfn> =&gt; <var>integer</var> 3</dt></dl><dl><dt><dfn>pageSelector</dfn> =&gt; <var>string</var> (4) \"page\"</dt></dl><dl><dt><dfn>currentPage</dfn> =&gt; <var>integer</var> 2</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>custom_pagination</dfn> =&gt; <var>array</var> (8)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>currentUri</dfn> =&gt; <var>CodeIgniter\\HTTP\\SiteURI</var>#99 (20)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (20)</li><li>Methods (51)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (5) \"admin\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"artikel\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (24) \"/index.php/admin/artikel\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>page</dfn> =&gt; <var>string</var> (1) \"2\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>baseURL</dfn> -&gt; <var>CodeIgniter\\HTTP\\URI</var>#24 (15)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (15)</li><li>Methods (40)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?string $uri = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param string|null $uri The URI to parse.\n *\n * @throws HTTPException\n *\n * @TODO null for param $uri should be removed.\n *      See https://www.php-fig.org/psr/psr-17/#26-urifactoryinterface\n */\n\n<small>Defined in .../HTTP/URI.php:256</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn>: <var>URI</var> Sets and overwrites any current URI information.</dt><dd><pre>/**\n * Sets and overwrites any current URI information.\n *\n * @return URI\n *\n * @throws HTTPException\n *\n * @deprecated 4.4.0 This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:302</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n *\n * Warning: For backwards-compatability this method\n * assumes URIs with the same host as baseURL should\n * be relative to the project's configuration.\n * This aspect of __toString() is deprecated and should be avoided.\n */\n\n<small>Defined in .../HTTP/URI.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the path portion of the URI.</dt><dd><pre>/**\n * Sets the path portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPath($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:816</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var> Sets the current baseURL.</dt><dd><pre>/**\n * Sets the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:834</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the current baseURL.</dt><dd><pre>/**\n * Returns the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:846</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:862</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url call.</dt><dd><pre>/**\n * Saves our parts from a parse_url call.\n *\n * @return void\n */\n\n<small>Defined in .../HTTP/URI.php:1036</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>baseURL</dfn> <var>string</var> (22) \"http://localhost:8080/\"</dt></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>basePathWithoutIndexPage</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>private readonly</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (9) \"index.php\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>baseSegments</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"index.php\"</dt></dl></dd></dl><dl><dt><var>private</var> <dfn>routePath</dfn> -&gt; <var>string</var> (13) \"admin/artikel\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\App $configApp, string $relativePath = '', ?string $host = null, ?string $scheme = null)</dfn></dt><dd><pre>/**\n * @param         string              $relativePath URI path relative to baseURL. May include\n *                                                  queries or fragments.\n * @param         string|null         $host         Optional current hostname.\n * @param         string|null         $scheme       Optional scheme. 'http' or 'https'.\n * @phpstan-param 'http'|'https'|null $scheme\n */\n\n<small>Defined in .../HTTP/SiteURI.php:94</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>parseRelativePath(string $relativePath)</dfn>: <var>array</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>determineBaseURL(Config\\App $configApp, ?string $host, ?string $scheme)</dfn>: <var>CodeIgniter\\HTTP\\URI</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getIndexPageRoutePath(string $routePath)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>normalizeBaseURL(Config\\App $configApp)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setBasePath()</dfn>: <var>void</var> Sets basePathWithoutIndexPage and baseSegments.</dt><dd><pre>/**\n * Sets basePathWithoutIndexPage and baseSegments.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the baseURL.</dt><dd><pre>/**\n * Returns the baseURL.\n *\n * @interal\n */\n\n<small>Defined in .../HTTP/SiteURI.php:244</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRoutePath()</dfn>: <var>string</var> Returns the URI path relative to baseURL.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL.\n *\n * @return string The Route path.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:262</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/SiteURI.php:278</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setRoutePath(string $routePath)</dfn>: <var>void</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n */\n\n<small>Defined in .../HTTP/SiteURI.php:288</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>convertToSegments(string $path)</dfn>: <var>array</var> Converts path to segments</dt><dd><pre>/**\n * Converts path to segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:304</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url() call.</dt><dd><pre>/**\n * Saves our parts from a parse_url() call.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:335</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>baseUrl($relativePath = '', ?string $scheme = null)</dfn>: <var>string</var> For base_url() helper.</dt><dd><pre>/**\n * For base_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>stringifyRelativePath($relativePath)</dfn>: <var>string</var></dt><dd><pre>/**\n * @param array|string $relativePath URI string or array of URI segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:401</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>siteUrl($relativePath = '', ?string $scheme = null, ?Config\\App $config = null)</dfn>: <var>string</var> For site_url() helper.</dt><dd><pre>/**\n * For site_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n * @param App|null     $config       Alternate configuration to use.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:419</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>currentUri</dfn> <var>string</var> (52) \"http://localhost:8080/index.php/admin/artikel?page=2\"</dt></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>uri</dfn> =&gt; <var>CodeIgniter\\HTTP\\SiteURI</var>#98 (20)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (20)</li><li>Methods (51)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (5) \"admin\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"artikel\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (24) \"/index.php/admin/artikel\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>baseURL</dfn> -&gt; <var>CodeIgniter\\HTTP\\URI</var>#24 (15)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (15)</li><li>Methods (40)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?string $uri = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param string|null $uri The URI to parse.\n *\n * @throws HTTPException\n *\n * @TODO null for param $uri should be removed.\n *      See https://www.php-fig.org/psr/psr-17/#26-urifactoryinterface\n */\n\n<small>Defined in .../HTTP/URI.php:256</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn>: <var>URI</var> Sets and overwrites any current URI information.</dt><dd><pre>/**\n * Sets and overwrites any current URI information.\n *\n * @return URI\n *\n * @throws HTTPException\n *\n * @deprecated 4.4.0 This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:302</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n *\n * Warning: For backwards-compatability this method\n * assumes URIs with the same host as baseURL should\n * be relative to the project's configuration.\n * This aspect of __toString() is deprecated and should be avoided.\n */\n\n<small>Defined in .../HTTP/URI.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the path portion of the URI.</dt><dd><pre>/**\n * Sets the path portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPath($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:816</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var> Sets the current baseURL.</dt><dd><pre>/**\n * Sets the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:834</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the current baseURL.</dt><dd><pre>/**\n * Returns the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:846</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:862</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url call.</dt><dd><pre>/**\n * Saves our parts from a parse_url call.\n *\n * @return void\n */\n\n<small>Defined in .../HTTP/URI.php:1036</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>baseURL</dfn> <var>string</var> (22) \"http://localhost:8080/\"</dt></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>basePathWithoutIndexPage</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>private readonly</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (9) \"index.php\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>baseSegments</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"index.php\"</dt></dl></dd></dl><dl><dt><var>private</var> <dfn>routePath</dfn> -&gt; <var>string</var> (13) \"admin/artikel\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\App $configApp, string $relativePath = '', ?string $host = null, ?string $scheme = null)</dfn></dt><dd><pre>/**\n * @param         string              $relativePath URI path relative to baseURL. May include\n *                                                  queries or fragments.\n * @param         string|null         $host         Optional current hostname.\n * @param         string|null         $scheme       Optional scheme. 'http' or 'https'.\n * @phpstan-param 'http'|'https'|null $scheme\n */\n\n<small>Defined in .../HTTP/SiteURI.php:94</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>parseRelativePath(string $relativePath)</dfn>: <var>array</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>determineBaseURL(Config\\App $configApp, ?string $host, ?string $scheme)</dfn>: <var>CodeIgniter\\HTTP\\URI</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getIndexPageRoutePath(string $routePath)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>normalizeBaseURL(Config\\App $configApp)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setBasePath()</dfn>: <var>void</var> Sets basePathWithoutIndexPage and baseSegments.</dt><dd><pre>/**\n * Sets basePathWithoutIndexPage and baseSegments.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the baseURL.</dt><dd><pre>/**\n * Returns the baseURL.\n *\n * @interal\n */\n\n<small>Defined in .../HTTP/SiteURI.php:244</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRoutePath()</dfn>: <var>string</var> Returns the URI path relative to baseURL.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL.\n *\n * @return string The Route path.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:262</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/SiteURI.php:278</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setRoutePath(string $routePath)</dfn>: <var>void</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n */\n\n<small>Defined in .../HTTP/SiteURI.php:288</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>convertToSegments(string $path)</dfn>: <var>array</var> Converts path to segments</dt><dd><pre>/**\n * Converts path to segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:304</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url() call.</dt><dd><pre>/**\n * Saves our parts from a parse_url() call.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:335</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>baseUrl($relativePath = '', ?string $scheme = null)</dfn>: <var>string</var> For base_url() helper.</dt><dd><pre>/**\n * For base_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>stringifyRelativePath($relativePath)</dfn>: <var>string</var></dt><dd><pre>/**\n * @param array|string $relativePath URI string or array of URI segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:401</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>siteUrl($relativePath = '', ?string $scheme = null, ?Config\\App $config = null)</dfn>: <var>string</var> For site_url() helper.</dt><dd><pre>/**\n * For site_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n * @param App|null     $config       Alternate configuration to use.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:419</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>uri</dfn> <var>string</var> (45) \"http://localhost:8080/index.php/admin/artikel\"</dt></dl></li></ul></dd></dl><dl><dt><dfn>hasMore</dfn> =&gt; <var>boolean</var> false</dt></dl><dl><dt><dfn>total</dfn> =&gt; <var>null</var></dt></dl><dl><dt><dfn>perPage</dfn> =&gt; <var>integer</var> 20</dt></dl><dl><dt><dfn>pageCount</dfn> =&gt; <var>integer</var> 1</dt></dl><dl><dt><dfn>pageSelector</dfn> =&gt; <var>string</var> (22) \"page_custom_pagination\"</dt></dl><dl><dt><dfn>currentPage</dfn> =&gt; <var>integer</var> 1</dt></dl></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>segment</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>config</dfn> -&gt; <var>Config\\Pager</var>#85 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (4)</li><li>Static methods (3)</li><li>Static properties (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>templates</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>default_full</dfn> =&gt; <var>string</var> (36) \"CodeIgniter\\Pager\\Views\\default_full\"</dt></dl><dl><dt><dfn>default_simple</dfn> =&gt; <var>string</var> (38) \"CodeIgniter\\Pager\\Views\\default_simple\"</dt></dl><dl><dt><dfn>default_head</dfn> =&gt; <var>string</var> (36) \"CodeIgniter\\Pager\\Views\\default_head\"</dt></dl><dl><dt><dfn>custom_pagination</dfn> =&gt; <var>string</var> (23) \"pager/custom_pagination\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>perPage</dfn> -&gt; <var>integer</var> 20</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn> Will attempt to get environment variables with names that match the propertie...</dt><dd><pre>/**\n * Will attempt to get environment variables with names\n * that match the properties of the child class.\n *\n * The \"shortPrefix\" is the lowercase-only config class name.\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:114</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>initEnvValue(&amp;$property, string $name, string $prefix, string $shortPrefix)</dfn>: <var>void</var> Initialization an environment-specific configuration setting</dt><dd><pre>/**\n * Initialization an environment-specific configuration setting\n *\n * @param array|bool|float|int|string|null $property\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:151</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getEnvValue(string $property, string $prefix, string $shortPrefix)</dfn>: <var>string|null</var> Retrieve an environment-specific configuration setting</dt><dd><pre>/**\n * Retrieve an environment-specific configuration setting\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>registerProperties()</dfn>: <var>void</var> Provides external libraries a simple way to register one or more options into...</dt><dd><pre>/**\n * Provides external libraries a simple way to register one or more\n * options into a config file.\n *\n * @return void\n *\n * @throws ReflectionException\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:237</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in .../Config/BaseConfig.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::setModules(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in .../Config/BaseConfig.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::reset()</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in .../Config/BaseConfig.php:100</small></pre></dd></dl></li><li><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrars</dfn> :: <var>array</var> (0)</dt></dl><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$override</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$didDiscovery</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$discovering</dfn> :: <var>boolean</var> false</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrarFile</dfn> :: <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$moduleConfig</dfn> :: <var>Config\\Modules</var>#7 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (2)</li><li>Static methods (1)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>enabled</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>public</var> <dfn>discoverInComposer</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>aliases</dfn> -&gt; <var>array</var> (5)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (6) \"events\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"filters\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (10) \"registrars\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (6) \"routes\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (8) \"services\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>composerPackages</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in .../Modules/Modules.php:46</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>shouldDiscover(string $alias)</dfn>: <var>bool</var> Should the application auto-discover the requested resource.</dt><dd><pre>/**\n * Should the application auto-discover the requested resource.\n */\n\n<small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in .../Modules/Modules.php:54</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Modules\\Modules::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in .../Modules/Modules.php:63</small></pre></dd></dl></li></ul></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>view</dfn> -&gt; <var>CodeIgniter\\View\\View</var>#87 (14)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (14)</li><li>Methods (17)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>data</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>pager</dfn> =&gt; <var>CodeIgniter\\Pager\\PagerRenderer</var>#101 (11)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (11)</li><li>Methods (26)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>first</dfn> -&gt; <var>integer</var> 1</dt></dl><dl><dt><var>protected</var> <dfn>last</dfn> -&gt; <var>integer</var> 1</dt></dl><dl><dt><var>protected</var> <dfn>current</dfn> -&gt; <var>integer</var> 1</dt></dl><dl><dt><var>protected</var> <dfn>total</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>pageCount</dfn> -&gt; <var>integer</var> 1</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>uri</dfn> -&gt; <var>CodeIgniter\\HTTP\\SiteURI</var>#98 (20)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (20)</li><li>Methods (51)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (5) \"admin\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"artikel\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (24) \"/index.php/admin/artikel\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>http</dfn> =&gt; <var>integer</var> 80</dt></dl><dl><dt><dfn>https</dfn> =&gt; <var>integer</var> 443</dt></dl><dl><dt><dfn>ftp</dfn> =&gt; <var>integer</var> 21</dt></dl><dl><dt><dfn>sftp</dfn> =&gt; <var>integer</var> 22</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private readonly</var> <dfn>baseURL</dfn> -&gt; <var>CodeIgniter\\HTTP\\URI</var>#24 (15)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (15)</li><li>Methods (40)</li><li>Static methods (2)</li><li>Class constants (2)</li><li>toString</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>protected</var> <dfn>uriString</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>private</var> <dfn>baseURL</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>segments</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>scheme</dfn> -&gt; <var>string</var> (4) \"http\"</dt></dl><dl><dt><var>protected</var> <dfn>user</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>password</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>host</dfn> -&gt; <var>string</var> (9) \"localhost\"</dt></dl><dl><dt><var>protected</var> <dfn>port</dfn> -&gt; <var>integer</var> 8080</dt></dl><dl><dt><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>protected</var> <dfn>fragment</dfn> -&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><var>protected</var> <dfn>query</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent kint-locked\"><nav></nav><var>protected</var> <dfn>defaultPorts</dfn> -&gt; <var>array</var> (4) <var>Depth Limit</var></dt></dl><dl><dt><var>protected</var> <dfn>showPassword</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>silent</dfn> -&gt; <var>boolean</var> false</dt></dl><dl><dt><var>protected</var> <dfn>rawQueryString</dfn> -&gt; <var>boolean</var> false</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(?string $uri = null)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param string|null $uri The URI to parse.\n *\n * @throws HTTPException\n *\n * @TODO null for param $uri should be removed.\n *      See https://www.php-fig.org/psr/psr-17/#26-urifactoryinterface\n */\n\n<small>Defined in .../HTTP/URI.php:256</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn>: <var>URI</var> Sets and overwrites any current URI information.</dt><dd><pre>/**\n * Sets and overwrites any current URI information.\n *\n * @return URI\n *\n * @throws HTTPException\n *\n * @deprecated 4.4.0 This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:302</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n *\n * Warning: For backwards-compatability this method\n * assumes URIs with the same host as baseURL should\n * be relative to the project's configuration.\n * This aspect of __toString() is deprecated and should be avoided.\n */\n\n<small>Defined in .../HTTP/URI.php:628</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the path portion of the URI.</dt><dd><pre>/**\n * Sets the path portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPath($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:816</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var> Sets the current baseURL.</dt><dd><pre>/**\n * Sets the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:834</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the current baseURL.</dt><dd><pre>/**\n * Returns the current baseURL.\n *\n * @interal\n *\n * @deprecated Use SiteURI instead.\n */\n\n<small>Defined in .../HTTP/URI.php:846</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/URI.php:862</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url call.</dt><dd><pre>/**\n * Saves our parts from a parse_url call.\n *\n * @return void\n */\n\n<small>Defined in .../HTTP/URI.php:1036</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>baseURL</dfn> <var>string</var> (22) \"http://localhost:8080/\"</dt></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>basePathWithoutIndexPage</dfn> -&gt; <var>string</var> (1) \"/\"</dt></dl><dl><dt><var>private readonly</var> <dfn>indexPage</dfn> -&gt; <var>string</var> (9) \"index.php\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>baseSegments</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"index.php\"</dt></dl></dd></dl><dl><dt><var>private</var> <dfn>routePath</dfn> -&gt; <var>string</var> (13) \"admin/artikel\"</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\App $configApp, string $relativePath = '', ?string $host = null, ?string $scheme = null)</dfn></dt><dd><pre>/**\n * @param         string              $relativePath URI path relative to baseURL. May include\n *                                                  queries or fragments.\n * @param         string|null         $host         Optional current hostname.\n * @param         string|null         $scheme       Optional scheme. 'http' or 'https'.\n * @phpstan-param 'http'|'https'|null $scheme\n */\n\n<small>Defined in .../HTTP/SiteURI.php:94</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>parseRelativePath(string $relativePath)</dfn>: <var>array</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>determineBaseURL(Config\\App $configApp, ?string $host, ?string $scheme)</dfn>: <var>CodeIgniter\\HTTP\\URI</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:142</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>getIndexPageRoutePath(string $routePath)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>normalizeBaseURL(Config\\App $configApp)</dfn>: <var>string</var></dt><dd><pre><small>Defined in .../HTTP/SiteURI.php:193</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setBasePath()</dfn>: <var>void</var> Sets basePathWithoutIndexPage and baseSegments.</dt><dd><pre>/**\n * Sets basePathWithoutIndexPage and baseSegments.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:212</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setBaseURL(string $baseURL)</dfn>: <var>void</var></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:226</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setURI(?string $uri = null)</dfn></dt><dd><pre>/**\n * @deprecated\n */\n\n<small>Defined in .../HTTP/SiteURI.php:234</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getBaseURL()</dfn>: <var>string</var> Returns the baseURL.</dt><dd><pre>/**\n * Returns the baseURL.\n *\n * @interal\n */\n\n<small>Defined in .../HTTP/SiteURI.php:244</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getRoutePath()</dfn>: <var>string</var> Returns the URI path relative to baseURL.</dt><dd><pre>/**\n * Returns the URI path relative to baseURL.\n *\n * @return string The Route path.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__toString()</dfn>: <var>string</var> Formats the URI as a string.</dt><dd><pre>/**\n * Formats the URI as a string.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:262</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPath(string $path)</dfn>: <var>$this</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/SiteURI.php:278</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>setRoutePath(string $routePath)</dfn>: <var>void</var> Sets the route path (and segments).</dt><dd><pre>/**\n * Sets the route path (and segments).\n */\n\n<small>Defined in .../HTTP/SiteURI.php:288</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>convertToSegments(string $path)</dfn>: <var>array</var> Converts path to segments</dt><dd><pre>/**\n * Converts path to segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:304</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>refreshPath()</dfn>: <var>$this</var> Sets the path portion of the URI based on segments.</dt><dd><pre>/**\n * Sets the path portion of the URI based on segments.\n *\n * @return $this\n *\n * @deprecated This method will be private.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>applyParts(array $parts)</dfn>: <var>void</var> Saves our parts from a parse_url() call.</dt><dd><pre>/**\n * Saves our parts from a parse_url() call.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:335</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>baseUrl($relativePath = '', ?string $scheme = null)</dfn>: <var>string</var> For base_url() helper.</dt><dd><pre>/**\n * For base_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:379</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>stringifyRelativePath($relativePath)</dfn>: <var>string</var></dt><dd><pre>/**\n * @param array|string $relativePath URI string or array of URI segments\n */\n\n<small>Defined in .../HTTP/SiteURI.php:401</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>siteUrl($relativePath = '', ?string $scheme = null, ?Config\\App $config = null)</dfn>: <var>string</var> For site_url() helper.</dt><dd><pre>/**\n * For site_url() helper.\n *\n * @param array|string $relativePath URI string or array of URI segments.\n * @param string|null  $scheme       URI scheme. E.g., http, ftp. If empty\n *                                   string '' is set, a protocol-relative\n *                                   link is returned.\n * @param App|null     $config       Alternate configuration to use.\n */\n\n<small>Defined in .../HTTP/SiteURI.php:419</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSilent(bool $silent = true)</dfn>: <var>URI</var> If $silent == true, then will not throw exceptions and will attempt to contin...</dt><dd><pre>/**\n * If $silent == true, then will not throw exceptions and will\n * attempt to continue gracefully.\n *\n * @deprecated 4.4.0 Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:271</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>useRawQueryString(bool $raw = true)</dfn>: <var>URI</var> If $raw == true, then will use parseStr() method instead of native parse_str(...</dt><dd><pre>/**\n * If $raw == true, then will use parseStr() method\n * instead of native parse_str() function.\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:286</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getScheme()</dfn>: <var>string</var> Retrieve the scheme component of the URI.</dt><dd><pre>/**\n * Retrieve the scheme component of the URI.\n *\n * If no scheme is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.1.\n *\n * The trailing \":\" character is not part of the scheme and MUST NOT be\n * added.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-3.1\n *\n * @return string The URI scheme.\n */\n\n<small>Defined in .../HTTP/URI.php:336</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getAuthority(bool $ignorePort = false)</dfn>: <var>string</var> Retrieve the authority component of the URI.</dt><dd><pre>/**\n * Retrieve the authority component of the URI.\n *\n * If no authority information is present, this method MUST return an empty\n * string.\n *\n * The authority syntax of the URI is:\n *\n * &lt;pre&gt;\n * [user-info@]host[:port]\n * &lt;/pre&gt;\n *\n * If the port component is not set or is the standard port for the current\n * scheme, it SHOULD NOT be included.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.2\n *\n * @return string The URI authority, in \"[user-info@]host[:port]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:360</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getUserInfo()</dfn>: <var>string|null The URI user information, in \"username[:password]\" format.</var> Retrieve the user information component of the URI.</dt><dd><pre>/**\n * Retrieve the user information component of the URI.\n *\n * If no user information is present, this method MUST return an empty\n * string.\n *\n * If a user is present in the URI, this will return that value;\n * additionally, if the password is also present, it will be appended to the\n * user value, with a colon (\":\") separating the values.\n *\n * NOTE that be default, the password, if available, will NOT be shown\n * as a security measure as discussed in RFC 3986, Section 7.5. If you know\n * the password is not a security issue, you can force it to be shown\n * with $this-&gt;showPassword();\n *\n * The trailing \"@\" character is not part of the user information and MUST\n * NOT be added.\n *\n * @return string|null The URI user information, in \"username[:password]\" format.\n */\n\n<small>Defined in .../HTTP/URI.php:403</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>showPassword(bool $val = true)</dfn>: <var>URI</var> Temporarily sets the URI to show a password in userInfo. Will reset itself af...</dt><dd><pre>/**\n * Temporarily sets the URI to show a password in userInfo. Will\n * reset itself after the first call to authority().\n *\n * Note: Method not in PSR-7\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:422</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getHost()</dfn>: <var>string</var> Retrieve the host component of the URI.</dt><dd><pre>/**\n * Retrieve the host component of the URI.\n *\n * If no host is present, this method MUST return an empty string.\n *\n * The value returned MUST be normalized to lowercase, per RFC 3986\n * Section 3.2.2.\n *\n * @see    http://tools.ietf.org/html/rfc3986#section-3.2.2\n *\n * @return string The URI host.\n */\n\n<small>Defined in .../HTTP/URI.php:441</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPort()</dfn>: <var>int|null The URI port.</var> Retrieve the port component of the URI.</dt><dd><pre>/**\n * Retrieve the port component of the URI.\n *\n * If a port is present, and it is non-standard for the current scheme,\n * this method MUST return it as an integer. If the port is the standard port\n * used with the current scheme, this method SHOULD return null.\n *\n * If no port is present, and no scheme is present, this method MUST return\n * a null value.\n *\n * If no port is present, but a scheme is present, this method MAY return\n * the standard port for that scheme, but SHOULD return null.\n *\n * @return int|null The URI port.\n */\n\n<small>Defined in .../HTTP/URI.php:461</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPath()</dfn>: <var>string</var> Retrieve the path component of the URI.</dt><dd><pre>/**\n * Retrieve the path component of the URI.\n *\n * The path can either be empty or absolute (starting with a slash) or\n * rootless (not starting with a slash). Implementations MUST support all\n * three syntaxes.\n *\n * Normally, the empty path \"\" and absolute path \"/\" are considered equal as\n * defined in RFC 7230 Section 2.7.3. But this method MUST NOT automatically\n * do this normalization because in contexts with a trimmed base path, e.g.\n * the front controller, this difference becomes significant. It's the task\n * of the user to handle both \"\" and \"/\".\n *\n * The value returned MUST be percent-encoded, but MUST NOT double-encode\n * any characters. To determine what characters to encode, please refer to\n * RFC 3986, Sections 2 and 3.3.\n *\n * As an example, if the value should include a slash (\"/\") not intended as\n * delimiter between path segments, that value MUST be passed in encoded\n * form (e.g., \"%2F\") to the instance.\n *\n * @see    https://tools.ietf.org/html/rfc3986#section-2\n * @see    https://tools.ietf.org/html/rfc3986#section-3.3\n *\n * @return string The URI path.\n */\n\n<small>Defined in .../HTTP/URI.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getQuery(array $options = array())</dfn>: <var>string</var> Retrieve the query string</dt><dd><pre>/**\n * Retrieve the query string\n */\n\n<small>Defined in .../HTTP/URI.php:500</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFragment()</dfn>: <var>string</var> Retrieve a URI fragment</dt><dd><pre>/**\n * Retrieve a URI fragment\n */\n\n<small>Defined in .../HTTP/URI.php:534</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegments()</dfn>: <var>array</var> Returns the segments of the path as an array.</dt><dd><pre>/**\n * Returns the segments of the path as an array.\n */\n\n<small>Defined in .../HTTP/URI.php:542</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getSegment(int $number, string $default = '')</dfn>: <var>string</var> Returns the value of a specific segment of the URI path. Allows to get only e...</dt><dd><pre>/**\n * Returns the value of a specific segment of the URI path.\n * Allows to get only existing segments or the next one.\n *\n * @param int    $number  Segment number starting at 1\n * @param string $default Default value\n *\n * @return string The value of the segment. If you specify the last +1\n *                segment, the $default value. If you specify the last +2\n *                or more throws HTTPException.\n */\n\n<small>Defined in .../HTTP/URI.php:558</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSegment(int $number, $value)</dfn>: <var>$this</var> Set the value of a specific segment of the URI path. Allows to set only exist...</dt><dd><pre>/**\n * Set the value of a specific segment of the URI path.\n * Allows to set only existing segments or add new one.\n *\n * Note: Method not in PSR-7\n *\n * @param int        $number Segment number starting at 1\n * @param int|string $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:586</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotalSegments()</dfn>: <var>int</var> Returns the total number of segments.</dt><dd><pre>/**\n * Returns the total number of segments.\n *\n * Note: Method not in PSR-7\n */\n\n<small>Defined in .../HTTP/URI.php:615</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setAuthority(string $str)</dfn>: <var>$this</var> Parses the given string and saves the appropriate authority pieces.</dt><dd><pre>/**\n * Parses the given string and saves the appropriate authority pieces.\n *\n * Note: Method not in PSR-7\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:685</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setScheme(string $str)</dfn>: <var>$this</var> Sets the scheme for this URI.</dt><dd><pre>/**\n * Sets the scheme for this URI.\n *\n * Because of the large number of valid schemes we cannot limit this\n * to only http or https.\n *\n * @see https://www.iana.org/assignments/uri-schemes/uri-schemes.xhtml\n *\n * @return $this\n *\n * @deprecated 4.4.0 Use `withScheme()` instead.\n */\n\n<small>Defined in .../HTTP/URI.php:715</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>withScheme(string $scheme)</dfn>: <var>static A new instance with the specified scheme.</var> Return an instance with the specified scheme.</dt><dd><pre>/**\n * Return an instance with the specified scheme.\n *\n * This method MUST retain the state of the current instance, and return\n * an instance that contains the specified scheme.\n *\n * Implementations MUST support the schemes \"http\" and \"https\" case\n * insensitively, and MAY accommodate other schemes if required.\n *\n * An empty scheme is equivalent to removing the scheme.\n *\n * @param string $scheme The scheme to use with the new instance.\n *\n * @return static A new instance with the specified scheme.\n *\n * @throws InvalidArgumentException for invalid or unsupported schemes.\n */\n\n<small>Defined in .../HTTP/URI.php:740</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setUserInfo(string $user, string $pass)</dfn>: <var>$this</var> Sets the userInfo/Authority portion of the URI.</dt><dd><pre>/**\n * Sets the userInfo/Authority portion of the URI.\n *\n * @param string $user The user's username\n * @param string $pass The user's password\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withUserInfo($user, $password = null)`.\n */\n\n<small>Defined in .../HTTP/URI.php:761</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setHost(string $str)</dfn>: <var>$this</var> Sets the host name to use.</dt><dd><pre>/**\n * Sets the host name to use.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withHost($host)`.\n */\n\n<small>Defined in .../HTTP/URI.php:776</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setPort(?int $port = null)</dfn>: <var>$this</var> Sets the port portion of the URI.</dt><dd><pre>/**\n * Sets the port portion of the URI.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withPort($port)`.\n */\n\n<small>Defined in .../HTTP/URI.php:790</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQuery(string $query)</dfn>: <var>$this</var> Sets the query portion of the URI, while attempting to clean the various part...</dt><dd><pre>/**\n * Sets the query portion of the URI, while attempting\n * to clean the various parts of the query keys and values.\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withQuery($query)`.\n */\n\n<small>Defined in .../HTTP/URI.php:881</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setQueryArray(array $query)</dfn>: <var>URI</var> A convenience method to pass an array of items in as the Query portion of the...</dt><dd><pre>/**\n * A convenience method to pass an array of items in as the Query\n * portion of the URI.\n *\n * @return URI\n *\n * @TODO: PSR-7: Should be `withQueryParams(array $query)`\n */\n\n<small>Defined in .../HTTP/URI.php:913</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addQuery(string $key, $value = null)</dfn>: <var>$this</var> Adds a single new element to the query vars.</dt><dd><pre>/**\n * Adds a single new element to the query vars.\n *\n * Note: Method not in PSR-7\n *\n * @param int|string|null $value\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:929</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>stripQuery($params)</dfn>: <var>$this</var> Removes one or more query vars from the URI.</dt><dd><pre>/**\n * Removes one or more query vars from the URI.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:945</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>keepQuery($params)</dfn>: <var>$this</var> Filters the query variables so that only the keys passed in are kept. The res...</dt><dd><pre>/**\n * Filters the query variables so that only the keys passed in\n * are kept. The rest are removed from the object.\n *\n * Note: Method not in PSR-7\n *\n * @param string ...$params\n *\n * @return $this\n */\n\n<small>Defined in .../HTTP/URI.php:964</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setFragment(string $string)</dfn>: <var>$this</var> Sets the fragment portion of the URI.</dt><dd><pre>/**\n * Sets the fragment portion of the URI.\n *\n * @see https://tools.ietf.org/html/rfc3986#section-3.5\n *\n * @return $this\n *\n * @TODO PSR-7: Should be `withFragment($fragment)`.\n */\n\n<small>Defined in .../HTTP/URI.php:990</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>filterPath(?string $path = null)</dfn>: <var>string</var> Encodes any dangerous characters, and removes dot segments. While dot segment...</dt><dd><pre>/**\n * Encodes any dangerous characters, and removes dot segments.\n * While dot segments have valid uses according to the spec,\n * this URI class does not allow them.\n */\n\n<small>Defined in .../HTTP/URI.php:1002</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resolveRelativeURI(string $uri)</dfn>: <var>URI</var> Combines one URI string with this one based on the rules set out in RFC 3986 ...</dt><dd><pre>/**\n * Combines one URI string with this one based on the rules set out in\n * RFC 3986 Section 2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2\n *\n * @return URI\n */\n\n<small>Defined in .../HTTP/URI.php:1087</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>mergePaths(self $base, self $reference)</dfn>: <var>string</var> Given 2 paths, will merge them according to rules set out in RFC 2986, Sectio...</dt><dd><pre>/**\n * Given 2 paths, will merge them according to rules set out in RFC 2986,\n * Section 5.2\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.3\n */\n\n<small>Defined in .../HTTP/URI.php:1143</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>parseStr(string $query)</dfn>: <var>array</var> This is equivalent to the native PHP parse_str() function. This version allow...</dt><dd><pre>/**\n * This is equivalent to the native PHP parse_str() function.\n * This version allows the dot to be used as a key of the query string.\n */\n\n<small>Defined in .../HTTP/URI.php:1165</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>changeSchemeAndPath(string $scheme, string $path)</dfn>: <var>array</var> Change the path (and scheme) assuming URIs with the same host as baseURL shou...</dt><dd><pre>/**\n * Change the path (and scheme) assuming URIs with the same host as baseURL\n * should be relative to the project's configuration.\n *\n * @deprecated This method will be deleted.\n */\n\n<small>Defined in .../HTTP/URI.php:651</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::createURIString(?string $scheme = null, ?string $authority = null, ?string $path = null, ?string $query = null, ?string $fragment = null)</dfn>: <var>string</var> Builds a representation of the string from the component parts.</dt><dd><pre>/**\n * Builds a representation of the string from the component parts.\n *\n * @param string|null $scheme URI scheme. E.g., http, ftp\n *\n * @return string URI string with only passed parts. Maybe incomplete as a URI.\n */\n\n<small>Defined in .../HTTP/URI.php:161</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\HTTP\\URI::removeDotSegments(string $path)</dfn>: <var>string</var> Used when resolving and merging paths to correctly interpret and remove singl...</dt><dd><pre>/**\n * Used when resolving and merging paths to correctly interpret and\n * remove single and double dot segments from the path per\n * RFC 3986 Section 5.2.4\n *\n * @see http://tools.ietf.org/html/rfc3986#section-5.2.4\n *\n * @internal\n */\n\n<small>Defined in .../HTTP/URI.php:203</small></pre></dd></dl></li><li><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_SUB_DELIMS</dfn> :: <var>string</var> (16) \"!\\$&amp;'\\(\\)\\*\\+,;=\"</dt></dl><dl><dt><var>public const</var> <dfn>CodeIgniter\\HTTP\\URI::CHAR_UNRESERVED</dfn> :: <var>string</var> (15) \"a-zA-Z0-9_\\-\\.~\"</dt></dl></li><li><dl><dt><dfn>uri</dfn> <var>string</var> (45) \"http://localhost:8080/index.php/admin/artikel\"</dt></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>segment</dfn> -&gt; <var>integer</var> 0</dt></dl><dl><dt><var>protected</var> <dfn>pageSelector</dfn> -&gt; <var>string</var> (22) \"page_custom_pagination\"</dt></dl><dl><dt><var>protected</var> <dfn>perPage</dfn> -&gt; <var>integer</var> 20</dt></dl><dl><dt><var>protected</var> <dfn>perPageStart</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>perPageEnd</dfn> -&gt; <var>null</var></dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(array $details)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:103</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setSurroundCount(?int $count = null)</dfn>: <var>PagerRenderer</var> Sets the total number of links that should appear on either side of the curre...</dt><dd><pre>/**\n * Sets the total number of links that should appear on either\n * side of the current page. Adjusts the first and last counts\n * to reflect it.\n *\n * @return PagerRenderer\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:127</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hasPrevious()</dfn>: <var>bool</var> Checks to see if there is a \"previous\" page before our \"first\" page.</dt><dd><pre>/**\n * Checks to see if there is a \"previous\" page before our \"first\" page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:137</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPrevious()</dfn>: <var>string|null</var> Returns a URL to the \"previous\" page. The previous page is NOT the page befor...</dt><dd><pre>/**\n * Returns a URL to the \"previous\" page. The previous page is NOT the\n * page before the current page, but is the page just before the\n * \"first\" page.\n *\n * @return string|null\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:149</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hasNext()</dfn>: <var>bool</var> Checks to see if there is a \"next\" page after our \"last\" page.</dt><dd><pre>/**\n * Checks to see if there is a \"next\" page after our \"last\" page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:175</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getNext()</dfn>: <var>string|null</var> Returns a URL to the \"next\" page. The next page is NOT, the page after the cu...</dt><dd><pre>/**\n * Returns a URL to the \"next\" page. The next page is NOT, the\n * page after the current page, but is the page that follows the\n * \"last\" page.\n *\n * @return string|null\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:187</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFirst()</dfn>: <var>string</var> Returns the URI of the first page.</dt><dd><pre>/**\n * Returns the URI of the first page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:213</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getLast()</dfn>: <var>string</var> Returns the URI of the last page.</dt><dd><pre>/**\n * Returns the URI of the last page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:235</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getCurrent()</dfn>: <var>string</var> Returns the URI of the current page.</dt><dd><pre>/**\n * Returns the URI of the current page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:257</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>links()</dfn>: <var>array</var> Returns an array of links that should be displayed. Each link is represented ...</dt><dd><pre>/**\n * Returns an array of links that should be displayed. Each link\n * is represented by another array containing of the URI the link\n * should go to, the title (number) of the link, and a boolean\n * value representing whether this link is active or not.\n *\n * @return list&lt;array{uri:string, title:int, active:bool}&gt;\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:284</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>updatePages(?int $count = null)</dfn>: <var>void</var> Updates the first and last pages based on $surroundCount, which is the number...</dt><dd><pre>/**\n * Updates the first and last pages based on $surroundCount,\n * which is the number of links surrounding the active page\n * to show.\n *\n * @param int|null $count The new \"surroundCount\"\n *\n * @return void\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:317</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>updatePerPages()</dfn>: <var>void</var> Updates the start and end items per pages, which is the number of items displ...</dt><dd><pre>/**\n * Updates the start and end items per pages, which is\n * the number of items displayed on the active page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:331</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hasPreviousPage()</dfn>: <var>bool</var> Checks to see if there is a \"previous\" page before our \"first\" page.</dt><dd><pre>/**\n * Checks to see if there is a \"previous\" page before our \"first\" page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:352</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPreviousPage()</dfn>: <var>string|null</var> Returns a URL to the \"previous\" page.</dt><dd><pre>/**\n * Returns a URL to the \"previous\" page.\n *\n * You MUST call hasPreviousPage() first, or this value may be invalid.\n *\n * @return string|null\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:364</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>hasNextPage()</dfn>: <var>bool</var> Checks to see if there is a \"next\" page after our \"last\" page.</dt><dd><pre>/**\n * Checks to see if there is a \"next\" page after our \"last\" page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:390</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getNextPage()</dfn>: <var>string|null</var> Returns a URL to the \"next\" page.</dt><dd><pre>/**\n * Returns a URL to the \"next\" page.\n *\n * You MUST call hasNextPage() first, or this value may be invalid.\n *\n * @return string|null\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:402</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getFirstPageNumber()</dfn>: <var>int</var> Returns the page number of the first page in the set of links to be displayed.</dt><dd><pre>/**\n * Returns the page number of the first page in the set of links to be displayed.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:428</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getCurrentPageNumber()</dfn>: <var>int</var> Returns the page number of the current page.</dt><dd><pre>/**\n * Returns the page number of the current page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:436</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getLastPageNumber()</dfn>: <var>int</var> Returns the page number of the last page in the set of links to be displayed.</dt><dd><pre>/**\n * Returns the page number of the last page in the set of links to be displayed.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:444</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPageCount()</dfn>: <var>int</var> Returns total number of pages.</dt><dd><pre>/**\n * Returns total number of pages.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:452</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPreviousPageNumber()</dfn>: <var>?int</var> Returns the previous page number.</dt><dd><pre>/**\n * Returns the previous page number.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:460</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getNextPageNumber()</dfn>: <var>?int</var> Returns the next page number.</dt><dd><pre>/**\n * Returns the next page number.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:468</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getTotal()</dfn>: <var>?int</var> Returns the total items of the page.</dt><dd><pre>/**\n * Returns the total items of the page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:476</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPerPage()</dfn>: <var>?int</var> Returns the number of items to be displayed on the page.</dt><dd><pre>/**\n * Returns the number of items to be displayed on the page.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:484</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPerPageStart()</dfn>: <var>?int</var> Returns the number of items the page starts with.</dt><dd><pre>/**\n * Returns the number of items the page starts with.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:492</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPerPageEnd()</dfn>: <var>?int</var> Returns the number of items the page ends with.</dt><dd><pre>/**\n * Returns the number of items the page ends with.\n */\n\n<small>Defined in .../Pager/PagerRenderer.php:500</small></pre></dd></dl></li></ul></dd></dl></dd></dl><dl><dt><var>protected</var> <dfn>tempData</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>viewPath</dfn> -&gt; <var>string</var> (49) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\app\\Config/../Views\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 25 15:25 C:\\xampp\\htdocs\\lab11_ci\\ci4\\app\\Views\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>renderVars</dfn> -&gt; <var>array</var> (4)</dt><dd><dl><dt><dfn>start</dfn> =&gt; <var>double</var> **********.8005</dt></dl><dl><dt><dfn>view</dfn> =&gt; <var>string</var> (40) \"CodeIgniter\\Pager\\Views\\default_full.php\"</dt></dl><dl><dt><dfn>options</dfn> =&gt; <var>array</var> (0)</dt></dl><dl><dt><dfn>file</dfn> =&gt; <var>string</var> (41) \"1 SYSTEMPATH\\Pager\\Views\\default_full.php\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loader</dfn> -&gt; <var>CodeIgniter\\Autoloader\\FileLocator</var>#5 (2)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (2)</li><li>Methods (10)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>autoloader</dfn> -&gt; <var>CodeIgniter\\Autoloader\\Autoloader</var>#2 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (18)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>prefixes</dfn> -&gt; <var>array</var> (3)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>CodeIgniter</dfn> =&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (36) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Mar 20 15:26 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\n</pre></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Config</dfn> =&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (40) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\app\\Config\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Mar 20 15:26 C:\\xampp\\htdocs\\lab11_ci\\ci4\\app\\Config\n</pre></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>App</dfn> =&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>string</var> (33) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\app\\\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 22 07:31 C:\\xampp\\htdocs\\lab11_ci\\ci4\\app\n</pre></li></ul></dd></dl></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>classmap</dfn> -&gt; <var>array</var> (12)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\AbstractLogger</dfn> =&gt; <var>string</var> (73) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/AbstractLogger.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (414B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/AbstractLogger.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\InvalidArgumentException</dfn> =&gt; <var>string</var> (83) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/InvalidArgumentExcept...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (96B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/InvalidArgumentException.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/InvalidArgumentException.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerAwareInterface</dfn> =&gt; <var>string</var> (79) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerAwareInterface....</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (231B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerAwareInterface.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerAwareInterface.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerAwareTrait</dfn> =&gt; <var>string</var> (75) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerAwareTrait.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (347B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerAwareTrait.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerInterface</dfn> =&gt; <var>string</var> (74) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerInterface.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (2.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerInterface.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LoggerTrait</dfn> =&gt; <var>string</var> (70) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerTrait.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (2.7KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LoggerTrait.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\LogLevel</dfn> =&gt; <var>string</var> (67) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LogLevel.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (336B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/LogLevel.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Psr\\Log\\NullLogger</dfn> =&gt; <var>string</var> (69) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/NullLogger.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (643B)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/PSR/Log/NullLogger.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Exception\\ExceptionInterface</dfn> =&gt; <var>string</var> (87) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/ExceptionIn...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (138B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/ExceptionInterface.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/ExceptionInterface.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Exception\\InvalidArgumentException</dfn> =&gt; <var>string</var> (93) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/InvalidArgu...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (212B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/InvalidArgumentException.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/InvalidArgumentException.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Exception\\RuntimeException</dfn> =&gt; <var>string</var> (85) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/RuntimeExce...</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (196B)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/RuntimeException.php\n</pre></li><li><pre>C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Exception/RuntimeException.php\n</pre></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>Laminas\\Escaper\\Escaper</dfn> =&gt; <var>string</var> (66) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Escaper.php\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">File (12.4KB)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>-rw-rw-rw- 0 <USER> <GROUP> Mar 13 15:29 C:\\xampp\\htdocs\\lab11_ci\\ci4\\system\\ThirdParty/Escaper/Escaper.php\n</pre></li></ul></dd></dl></dd></dl><dl><dt><var>protected</var> <dfn>files</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>helpers</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (3) \"url\"</dt></dl></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>initialize(Config\\Autoload $config, Config\\Modules $modules)</dfn>: <var>$this</var> Reads in the configuration array (described above) and stores the valid parts...</dt><dd><pre>/**\n * Reads in the configuration array (described above) and stores\n * the valid parts that we'll need.\n *\n * @return $this\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:102</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>loadComposerAutoloader(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre><small>Defined in .../Autoloader/Autoloader.php:137</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>register()</dfn>: <var>void</var> Register the loader with the SPL autoloader stack.</dt><dd><pre>/**\n * Register the loader with the SPL autoloader stack.\n *\n * @return void\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:162</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>unregister()</dfn>: <var>void</var> Unregister autoloader.</dt><dd><pre>/**\n * Unregister autoloader.\n *\n * This method is for testing.\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:181</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>addNamespace($namespace, ?string $path = null)</dfn>: <var>$this</var> Registers namespaces with the autoloader.</dt><dd><pre>/**\n * Registers namespaces with the autoloader.\n *\n * @param array&lt;string, list&lt;string&gt;|string&gt;|string $namespace\n *\n * @return $this\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:194</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getNamespace(?string $prefix = null)</dfn>: <var>array&lt;string, list&lt;string&gt;&gt;|list&lt;string&gt;</var> Get namespaces with prefixes as keys and paths as values.</dt><dd><pre>/**\n * Get namespaces with prefixes as keys and paths as values.\n *\n * If a prefix param is set, returns only paths to the given prefix.\n *\n * @return         array&lt;string, list&lt;string&gt;&gt;|list&lt;string&gt;\n * @phpstan-return ($prefix is null ? array&lt;string, list&lt;string&gt;&gt; : list&lt;string&gt;)\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:225</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>removeNamespace(string $namespace)</dfn>: <var>$this</var> Removes a single namespace from the psr4 settings.</dt><dd><pre>/**\n * Removes a single namespace from the psr4 settings.\n *\n * @return $this\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:239</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>loadClassmap(string $class)</dfn>: <var>void</var> Load a class using available class mapping.</dt><dd><pre>/**\n * Load a class using available class mapping.\n *\n * @internal For `spl_autoload_register` use.\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:253</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>loadClass(string $class)</dfn>: <var>void</var> Loads the class file for a given class name.</dt><dd><pre>/**\n * Loads the class file for a given class name.\n *\n * @internal For `spl_autoload_register` use.\n *\n * @param string $class The fully qualified class name.\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:269</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loadInNamespace(string $class)</dfn>: <var>false|string The mapped file name on success, or boolean false on fail</var> Loads the class file for a given class name.</dt><dd><pre>/**\n * Loads the class file for a given class name.\n *\n * @param string $class The fully-qualified class name\n *\n * @return false|string The mapped file name on success, or boolean false on fail\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:281</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>includeFile(string $file)</dfn>: <var>false|string The filename on success, false if the file is not loaded</var> A central way to include a file. Split out primarily for testing purposes.</dt><dd><pre>/**\n * A central way to include a file. Split out primarily for testing purposes.\n *\n * @return false|string The filename on success, false if the file is not loaded\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:313</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>sanitizeFilename(string $filename)</dfn>: <var>string</var> Check file path.</dt><dd><pre>/**\n * Check file path.\n *\n * Checks special characters that are illegal in filenames on certain\n * operating systems and special characters requiring special escaping\n * to manipulate at the command line. Replaces spaces and consecutive\n * dashes with a single dash. Trim period, dash and underscore from beginning\n * and end of filename.\n *\n * @return string The sanitized filename\n *\n * @deprecated No longer used. See https://github.com/codeigniter4/CodeIgniter4/issues/7055\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:337</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>loadComposerNamespaces(Composer\\Autoload\\ClassLoader $composer, array $composerPackages)</dfn>: <var>void</var></dt><dd><pre>/**\n * @param array{only?: list&lt;string&gt;, exclude?: list&lt;string&gt;} $composerPackages\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:372</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>discoverComposerNamespaces()</dfn>: <var>void</var> Locates autoload information from Composer, if available.</dt><dd><pre>/**\n * Locates autoload information from Composer, if available.\n *\n * @deprecated No longer used.\n *\n * @return void\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:453</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>loadHelpers()</dfn>: <var>void</var> Loads helpers</dt><dd><pre>/**\n * Loads helpers\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:487</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>initializeKint(bool $debug = false)</dfn>: <var>void</var> Initializes Kint</dt><dd><pre>/**\n * Initializes Kint\n */\n\n<small>Defined in .../Autoloader/Autoloader.php:495</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>autoloadKint()</dfn>: <var>void</var></dt><dd><pre><small>Defined in .../Autoloader/Autoloader.php:508</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>private</var> <dfn>configureKint()</dfn>: <var>void</var></dt><dd><pre><small>Defined in .../Autoloader/Autoloader.php:530</small></pre></dd></dl></li></ul></dd></dl><dl><dt><var>private</var> <dfn>invalidClassnames</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(CodeIgniter\\Autoloader\\Autoloader $autoloader)</dfn></dt><dd><pre><small>Defined in .../Autoloader/FileLocator.php:38</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>locateFile(string $file, ?string $folder = null, string $ext = 'php')</dfn>: <var>false|string The path to the file, or false if not found.</var> Attempts to locate a file by examining the name for a namespace and looking t...</dt><dd><pre>/**\n * Attempts to locate a file by examining the name for a namespace\n * and looking through the PSR-4 namespaced files that we know about.\n *\n * @param string                $file   The relative file path or namespaced file to\n *                                      locate. If not namespaced, search in the app\n *                                      folder.\n * @param non-empty-string|null $folder The folder within the namespace that we should\n *                                      look for the file. If $file does not contain\n *                                      this value, it will be appended to the namespace\n *                                      folder.\n * @param string                $ext    The file extension the file should have.\n *\n * @return false|string The path to the file, or false if not found.\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:58</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getClassname(string $file)</dfn>: <var>string</var> Examines a file and returns the fully qualified class name.</dt><dd><pre>/**\n * Examines a file and returns the fully qualified class name.\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:129</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>search(string $path, string $ext = 'php', bool $prioritizeApp = true)</dfn>: <var>array</var> Searches through all of the defined namespaces looking for a file. Returns an...</dt><dd><pre>/**\n * Searches through all of the defined namespaces looking for a file.\n * Returns an array of all found locations for the defined file.\n *\n * Example:\n *\n *  $locator-&gt;search('Config/Routes.php');\n *  // Assuming PSR4 namespaces include foo and bar, might return:\n *  [\n *      'app/Modules/foo/Config/Routes.php',\n *      'app/Modules/bar/Config/Routes.php',\n *  ]\n *\n * @return list&lt;string&gt;\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>ensureExt(string $path, string $ext)</dfn>: <var>string</var> Ensures a extension is at the end of a filename</dt><dd><pre>/**\n * Ensures a extension is at the end of a filename\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:223</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getNamespaces()</dfn>: <var>array&lt;int, array&lt;string, string&gt;&gt;</var> Return the namespace mappings we know about.</dt><dd><pre>/**\n * Return the namespace mappings we know about.\n *\n * @return array&lt;int, array&lt;string, string&gt;&gt;\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:241</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>findQualifiedNameFromPath(string $path)</dfn>: <var>false|string The qualified name or false if the path is not found</var> Find the qualified name of a file according to the namespace of the first mat...</dt><dd><pre>/**\n * Find the qualified name of a file according to\n * the namespace of the first matched namespace path.\n *\n * @return false|string The qualified name or false if the path is not found\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:275</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>listFiles(string $path)</dfn>: <var>array</var> Scans the defined namespaces, returning a list of all files that are containe...</dt><dd><pre>/**\n * Scans the defined namespaces, returning a list of all files\n * that are contained within the subpath specified by $path.\n *\n * @return list&lt;string&gt; List of file paths\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:328</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>listNamespaceFiles(string $prefix, string $path)</dfn>: <var>array</var> Scans the provided namespace, returning a list of all files that are containe...</dt><dd><pre>/**\n * Scans the provided namespace, returning a list of all files\n * that are contained within the sub path specified by $path.\n *\n * @return list&lt;string&gt; List of file paths\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:362</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>legacyLocate(string $file, ?string $folder = null)</dfn>: <var>false|string The path to the file, or false if not found.</var> Checks the app folder to see if the file can be found. Only for use with file...</dt><dd><pre>/**\n * Checks the app folder to see if the file can be found.\n * Only for use with filenames that DO NOT include namespacing.\n *\n * @param non-empty-string|null $folder\n *\n * @return false|string The path to the file, or false if not found.\n */\n\n<small>Defined in .../Autoloader/FileLocator.php:399</small></pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>logger</dfn> -&gt; <var>CodeIgniter\\Log\\Logger</var>#71 (9)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (9)</li><li>Methods (12)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>logLevels</dfn> -&gt; <var>array</var> (8)</dt><dd><dl><dt><dfn>emergency</dfn> =&gt; <var>integer</var> 1</dt></dl><dl><dt><dfn>alert</dfn> =&gt; <var>integer</var> 2</dt></dl><dl><dt><dfn>critical</dfn> =&gt; <var>integer</var> 3</dt></dl><dl><dt><dfn>error</dfn> =&gt; <var>integer</var> 4</dt></dl><dl><dt><dfn>warning</dfn> =&gt; <var>integer</var> 5</dt></dl><dl><dt><dfn>notice</dfn> =&gt; <var>integer</var> 6</dt></dl><dl><dt><dfn>info</dfn> =&gt; <var>integer</var> 7</dt></dl><dl><dt><dfn>debug</dfn> =&gt; <var>integer</var> 8</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>loggableLevels</dfn> -&gt; <var>array</var> (9)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (9) \"emergency\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (5) \"alert\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (8) \"critical\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (5) \"error\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl><dl><dt><dfn>5</dfn> =&gt; <var>string</var> (6) \"notice\"</dt></dl><dl><dt><dfn>6</dfn> =&gt; <var>string</var> (4) \"info\"</dt></dl><dl><dt><dfn>7</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt><dfn>8</dfn> =&gt; <var>boolean</var> false</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>filePermissions</dfn> -&gt; <var>integer</var> 420</dt></dl><dl><dt><var>protected</var> <dfn>dateFormat</dfn> -&gt; <var>string</var> (11) \"Y-m-d H:i:s\"</dt></dl><dl><dt><var>protected</var> <dfn>fileExt</dfn> -&gt; <var>null</var></dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>handlers</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>CodeIgniter\\Log\\Handlers\\FileHandler</dfn> =&gt; <var>CodeIgniter\\Log\\Handlers\\FileHandler</var>#73 (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (5)</li><li>Methods (4)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>handles</dfn> -&gt; <var>array</var> (8)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (8) \"critical\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (5) \"alert\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (9) \"emergency\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (5) \"error\"</dt></dl><dl><dt><dfn>5</dfn> =&gt; <var>string</var> (4) \"info\"</dt></dl><dl><dt><dfn>6</dfn> =&gt; <var>string</var> (6) \"notice\"</dt></dl><dl><dt><dfn>7</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl></dd></dl><dl><dt><var>protected</var> <dfn>dateFormat</dfn> -&gt; <var>string</var> (11) \"Y-m-d H:i:s\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>path</dfn> -&gt; <var>string</var> (43) \"C:\\xampp\\htdocs\\lab11_ci\\ci4\\writable\\logs/\"</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Directory</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre>drwxrwxrwx 0 <USER> <GROUP> Jun 25 12:28 C:\\xampp\\htdocs\\lab11_ci\\ci4\\writable\\logs\n</pre></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>fileExtension</dfn> -&gt; <var>string</var> (3) \"log\"</dt></dl><dl><dt><var>protected</var> <dfn>filePermissions</dfn> -&gt; <var>integer</var> 420</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(array $config = array())</dfn> Constructor</dt><dd><pre>/**\n * Constructor\n */\n\n<small>Defined in .../Log/Handlers/FileHandler.php:50</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>handle($level, $message)</dfn>: <var>bool</var> Handles logging the message. If the handler returns false, then execution of ...</dt><dd><pre>/**\n * Handles logging the message.\n * If the handler returns false, then execution of handlers\n * will stop. Any handlers that have not run, yet, will not\n * be run.\n *\n * @param string $level\n * @param string $message\n *\n * @throws Exception\n */\n\n<small>Defined in .../Log/Handlers/FileHandler.php:73</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>canHandle(string $level)</dfn>: <var>bool</var> Checks whether the Handler will handle logging items of this log Level.</dt><dd><pre>/**\n * Checks whether the Handler will handle logging items of this\n * log Level.\n */\n\n<small>Inherited from CodeIgniter\\Log\\Handlers\\BaseHandler\nDefined in .../Log/Handlers/BaseHandler.php:47</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setDateFormat(string $format)</dfn>: <var>CodeIgniter\\Log\\Handlers\\HandlerInterface</var> Stores the date format to use while logging messages.</dt><dd><pre>/**\n * Stores the date format to use while logging messages.\n */\n\n<small>Inherited from CodeIgniter\\Log\\Handlers\\BaseHandler\nDefined in .../Log/Handlers/BaseHandler.php:55</small></pre></dd></dl></li></ul></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>handlerConfig</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>CodeIgniter\\Log\\Handlers\\FileHandler</dfn> =&gt; <var>array</var> (4)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>handles</dfn> =&gt; <var>array</var> (8)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (8) \"critical\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (5) \"alert\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (9) \"emergency\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (5) \"error\"</dt></dl><dl><dt><dfn>5</dfn> =&gt; <var>string</var> (4) \"info\"</dt></dl><dl><dt><dfn>6</dfn> =&gt; <var>string</var> (6) \"notice\"</dt></dl><dl><dt><dfn>7</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl></dd></dl><dl><dt><dfn>fileExtension</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl><dl><dt><dfn>filePermissions</dfn> =&gt; <var>integer</var> 420</dt></dl><dl><dt><dfn>path</dfn> =&gt; <var>string</var> (0) \"\"</dt></dl></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>logCache</dfn> -&gt; <var>array</var> (3)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (3)</li><li>Contents (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>level</th><th>msg</th></tr></thead><tbody><tr><th>0</th><td title=\"string (7)\">warning</td><td title=\"string (1048)\">[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercaUTF-8</td></tr><tr><th>1</th><td title=\"string (7)\">warning</td><td title=\"string (1052)\">[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercUTF-8</td></tr><tr><th>2</th><td title=\"string (5)\">debug</td><td title=\"string (83)\">Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler'UTF-8</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>level</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>msg</dfn> =&gt; <var>string</var> (1048) \"[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercas...</dt><dd><pre>[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection-&gt;match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection-&gt;loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter-&gt;tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter-&gt;handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter-&gt;run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>level</dfn> =&gt; <var>string</var> (7) \"warning\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>msg</dfn> =&gt; <var>string</var> (1052) \"[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use upperca...</dt><dd><pre>[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection-&gt;match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection-&gt;loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter-&gt;tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter-&gt;handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter-&gt;run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (2)</dt><dd><dl><dt><dfn>level</dfn> =&gt; <var>string</var> (5) \"debug\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><dfn>msg</dfn> =&gt; <var>string</var> (83) \"Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' ...</dt><dd><pre>Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver.\n</pre></dd></dl></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>cacheLogs</dfn> -&gt; <var>boolean</var> true</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct($config, bool $debug = true)</dfn> Constructor.</dt><dd><pre>/**\n * Constructor.\n *\n * @param \\Config\\Logger $config\n *\n * @throws RuntimeException\n */\n\n<small>Defined in .../Log/Logger.php:124</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>emergency(Stringable|string $message, array $context = array())</dfn>: <var>void</var> System is unusable.</dt><dd><pre>/**\n * System is unusable.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:162</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>alert(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Action must be taken immediately.</dt><dd><pre>/**\n * Action must be taken immediately.\n *\n * Example: Entire website down, database unavailable, etc. This should\n * trigger the SMS alerts and wake you up.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:175</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>critical(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Critical conditions.</dt><dd><pre>/**\n * Critical conditions.\n *\n * Example: Application component unavailable, unexpected exception.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:187</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>error(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Runtime errors that do not require immediate action but should typically be l...</dt><dd><pre>/**\n * Runtime errors that do not require immediate action but should typically\n * be logged and monitored.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:198</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>warning(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Exceptional occurrences that are not errors.</dt><dd><pre>/**\n * Exceptional occurrences that are not errors.\n *\n * Example: Use of deprecated APIs, poor use of an API, undesirable things\n * that are not necessarily wrong.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:211</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>notice(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Normal but significant events.</dt><dd><pre>/**\n * Normal but significant events.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:221</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>info(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Interesting events.</dt><dd><pre>/**\n * Interesting events.\n *\n * Example: User logs in, SQL logs.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:233</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>debug(Stringable|string $message, array $context = array())</dfn>: <var>void</var> Detailed debug information.</dt><dd><pre>/**\n * Detailed debug information.\n *\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:243</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>log($level, Stringable|string $message, array $context = array())</dfn>: <var>void</var> Logs with an arbitrary level.</dt><dd><pre>/**\n * Logs with an arbitrary level.\n *\n * @param string $level\n * @param string $message\n */\n\n<small>Defined in .../Log/Logger.php:254</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>interpolate($message, array $context = array())</dfn>: <var>string</var> Replaces any placeholders in the message with variables from the context, as ...</dt><dd><pre>/**\n * Replaces any placeholders in the message with variables\n * from the context, as well as a few special items like:\n *\n * {session_vars}\n * {post_vars}\n * {get_vars}\n * {env}\n * {env:foo}\n * {file}\n * {line}\n *\n * @param string $message\n *\n * @return string\n */\n\n<small>Defined in .../Log/Logger.php:318</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>determineFile()</dfn>: <var>array</var> Determines the file and line that the logging call was made from by analyzing...</dt><dd><pre>/**\n * Determines the file and line that the logging call\n * was made from by analyzing the backtrace.\n * Find the earliest stack frame that is part of our logging system.\n */\n\n<small>Defined in .../Log/Logger.php:374</small></pre></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>debug</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>performanceData</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt class=\"kint-parent\"><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (3)</dt><dd><dl><dt><dfn>start</dfn> =&gt; <var>double</var> **********.8005</dt></dl><dl><dt><dfn>end</dfn> =&gt; <var>double</var> **********.8038</dt></dl><dl><dt><dfn>view</dfn> =&gt; <var>string</var> (40) \"CodeIgniter\\Pager\\Views\\default_full.php\"</dt></dl></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>config</dfn> -&gt; <var>Config\\View</var>#86 (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (6)</li><li>Methods (4)</li><li>Static methods (3)</li><li>Static properties (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>saveData</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>filters</dfn> -&gt; <var>array</var> (21)</dt><dd><dl><dt><dfn>abs</dfn> =&gt; <var>string</var> (4) \"\\abs\"</dt></dl><dl><dt><dfn>capitalize</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Filters::capitalize\"</dt></dl><dl><dt><dfn>date</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Filters::date\"</dt></dl><dl><dt><dfn>date_modify</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::date_modify\"</dt></dl><dl><dt><dfn>default</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::default\"</dt></dl><dl><dt><dfn>esc</dfn> =&gt; <var>string</var> (30) \"\\CodeIgniter\\View\\Filters::esc\"</dt></dl><dl><dt><dfn>excerpt</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::excerpt\"</dt></dl><dl><dt><dfn>highlight</dfn> =&gt; <var>string</var> (36) \"\\CodeIgniter\\View\\Filters::highlight\"</dt></dl><dl><dt><dfn>highlight_code</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::highlight_code\"</dt></dl><dl><dt><dfn>limit_words</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_words\"</dt></dl><dl><dt><dfn>limit_chars</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_chars\"</dt></dl><dl><dt><dfn>local_currency</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::local_currency\"</dt></dl><dl><dt><dfn>local_number</dfn> =&gt; <var>string</var> (39) \"\\CodeIgniter\\View\\Filters::local_number\"</dt></dl><dl><dt><dfn>lower</dfn> =&gt; <var>string</var> (11) \"\\strtolower\"</dt></dl><dl><dt><dfn>nl2br</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::nl2br\"</dt></dl><dl><dt><dfn>number_format</dfn> =&gt; <var>string</var> (14) \"\\number_format\"</dt></dl><dl><dt><dfn>prose</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::prose\"</dt></dl><dl><dt><dfn>round</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::round\"</dt></dl><dl><dt><dfn>strip_tags</dfn> =&gt; <var>string</var> (11) \"\\strip_tags\"</dt></dl><dl><dt><dfn>title</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::title\"</dt></dl><dl><dt><dfn>upper</dfn> =&gt; <var>string</var> (11) \"\\strtoupper\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>plugins</dfn> -&gt; <var>array</var> (10)</dt><dd><dl><dt><dfn>csp_script_nonce</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Plugins::cspScriptNonce\"</dt></dl><dl><dt><dfn>csp_style_nonce</dfn> =&gt; <var>string</var> (40) \"\\CodeIgniter\\View\\Plugins::cspStyleNonce\"</dt></dl><dl><dt><dfn>current_url</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::currentURL\"</dt></dl><dl><dt><dfn>previous_url</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Plugins::previousURL\"</dt></dl><dl><dt><dfn>mailto</dfn> =&gt; <var>string</var> (33) \"\\CodeIgniter\\View\\Plugins::mailto\"</dt></dl><dl><dt><dfn>safe_mailto</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::safeMailto\"</dt></dl><dl><dt><dfn>lang</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Plugins::lang\"</dt></dl><dl><dt><dfn>validation_errors</dfn> =&gt; <var>string</var> (43) \"\\CodeIgniter\\View\\Plugins::validationErrors\"</dt></dl><dl><dt><dfn>route</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Plugins::route\"</dt></dl><dl><dt><dfn>siteURL</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Plugins::siteURL\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>coreFilters</dfn> -&gt; <var>array</var> (21)</dt><dd><dl><dt><dfn>abs</dfn> =&gt; <var>string</var> (4) \"\\abs\"</dt></dl><dl><dt><dfn>capitalize</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Filters::capitalize\"</dt></dl><dl><dt><dfn>date</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Filters::date\"</dt></dl><dl><dt><dfn>date_modify</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::date_modify\"</dt></dl><dl><dt><dfn>default</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::default\"</dt></dl><dl><dt><dfn>esc</dfn> =&gt; <var>string</var> (30) \"\\CodeIgniter\\View\\Filters::esc\"</dt></dl><dl><dt><dfn>excerpt</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Filters::excerpt\"</dt></dl><dl><dt><dfn>highlight</dfn> =&gt; <var>string</var> (36) \"\\CodeIgniter\\View\\Filters::highlight\"</dt></dl><dl><dt><dfn>highlight_code</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::highlight_code\"</dt></dl><dl><dt><dfn>limit_words</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_words\"</dt></dl><dl><dt><dfn>limit_chars</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Filters::limit_chars\"</dt></dl><dl><dt><dfn>local_currency</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Filters::local_currency\"</dt></dl><dl><dt><dfn>local_number</dfn> =&gt; <var>string</var> (39) \"\\CodeIgniter\\View\\Filters::local_number\"</dt></dl><dl><dt><dfn>lower</dfn> =&gt; <var>string</var> (11) \"\\strtolower\"</dt></dl><dl><dt><dfn>nl2br</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::nl2br\"</dt></dl><dl><dt><dfn>number_format</dfn> =&gt; <var>string</var> (14) \"\\number_format\"</dt></dl><dl><dt><dfn>prose</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::prose\"</dt></dl><dl><dt><dfn>round</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::round\"</dt></dl><dl><dt><dfn>strip_tags</dfn> =&gt; <var>string</var> (11) \"\\strip_tags\"</dt></dl><dl><dt><dfn>title</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Filters::title\"</dt></dl><dl><dt><dfn>upper</dfn> =&gt; <var>string</var> (11) \"\\strtoupper\"</dt></dl></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>corePlugins</dfn> -&gt; <var>array</var> (10)</dt><dd><dl><dt><dfn>csp_script_nonce</dfn> =&gt; <var>string</var> (41) \"\\CodeIgniter\\View\\Plugins::cspScriptNonce\"</dt></dl><dl><dt><dfn>csp_style_nonce</dfn> =&gt; <var>string</var> (40) \"\\CodeIgniter\\View\\Plugins::cspStyleNonce\"</dt></dl><dl><dt><dfn>current_url</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::currentURL\"</dt></dl><dl><dt><dfn>previous_url</dfn> =&gt; <var>string</var> (38) \"\\CodeIgniter\\View\\Plugins::previousURL\"</dt></dl><dl><dt><dfn>mailto</dfn> =&gt; <var>string</var> (33) \"\\CodeIgniter\\View\\Plugins::mailto\"</dt></dl><dl><dt><dfn>safe_mailto</dfn> =&gt; <var>string</var> (37) \"\\CodeIgniter\\View\\Plugins::safeMailto\"</dt></dl><dl><dt><dfn>lang</dfn> =&gt; <var>string</var> (31) \"\\CodeIgniter\\View\\Plugins::lang\"</dt></dl><dl><dt><dfn>validation_errors</dfn> =&gt; <var>string</var> (43) \"\\CodeIgniter\\View\\Plugins::validationErrors\"</dt></dl><dl><dt><dfn>route</dfn> =&gt; <var>string</var> (32) \"\\CodeIgniter\\View\\Plugins::route\"</dt></dl><dl><dt><dfn>siteURL</dfn> =&gt; <var>string</var> (34) \"\\CodeIgniter\\View\\Plugins::siteURL\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>decorators</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn> Merge the built-in and developer-configured filters and plugins, with prefere...</dt><dd><pre>/**\n * Merge the built-in and developer-configured filters and plugins,\n * with preference to the developer ones.\n */\n\n<small>Inherited from CodeIgniter\\Config\\View\nDefined in .../Config/View.php:129</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>initEnvValue(&amp;$property, string $name, string $prefix, string $shortPrefix)</dfn>: <var>void</var> Initialization an environment-specific configuration setting</dt><dd><pre>/**\n * Initialization an environment-specific configuration setting\n *\n * @param array|bool|float|int|string|null $property\n *\n * @return void\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:151</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>getEnvValue(string $property, string $prefix, string $shortPrefix)</dfn>: <var>string|null</var> Retrieve an environment-specific configuration setting</dt><dd><pre>/**\n * Retrieve an environment-specific configuration setting\n *\n * @return string|null\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:189</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>registerProperties()</dfn>: <var>void</var> Provides external libraries a simple way to register one or more options into...</dt><dd><pre>/**\n * Provides external libraries a simple way to register one or more\n * options into a config file.\n *\n * @return void\n *\n * @throws ReflectionException\n */\n\n<small>Inherited from CodeIgniter\\Config\\BaseConfig\nDefined in .../Config/BaseConfig.php:237</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in .../Config/BaseConfig.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::setModules(Config\\Modules $modules)</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in .../Config/BaseConfig.php:91</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::reset()</dfn>: <var>void</var></dt><dd><pre>/**\n * @internal For testing purposes only.\n * @testTag\n */\n\n<small>Defined in .../Config/BaseConfig.php:100</small></pre></dd></dl></li><li><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrars</dfn> :: <var>array</var> (0)</dt></dl><dl><dt><var>public static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$override</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$didDiscovery</dfn> :: <var>boolean</var> true</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$discovering</dfn> :: <var>boolean</var> false</dt></dl><dl><dt><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$registrarFile</dfn> :: <var>string</var> (0) \"\"</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected static</var> <dfn>CodeIgniter\\Config\\BaseConfig::$moduleConfig</dfn> :: <var>Config\\Modules</var>#7 (4)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Properties (4)</li><li>Methods (2)</li><li>Static methods (1)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><var>public</var> <dfn>enabled</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>public</var> <dfn>discoverInComposer</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>aliases</dfn> -&gt; <var>array</var> (5)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (6) \"events\"</dt></dl><dl><dt><dfn>1</dfn> =&gt; <var>string</var> (7) \"filters\"</dt></dl><dl><dt><dfn>2</dfn> =&gt; <var>string</var> (10) \"registrars\"</dt></dl><dl><dt><dfn>3</dfn> =&gt; <var>string</var> (6) \"routes\"</dt></dl><dl><dt><dfn>4</dfn> =&gt; <var>string</var> (8) \"services\"</dt></dl></dd></dl><dl><dt><var>public</var> <dfn>composerPackages</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct()</dfn></dt><dd><pre><small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in .../Modules/Modules.php:46</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>shouldDiscover(string $alias)</dfn>: <var>bool</var> Should the application auto-discover the requested resource.</dt><dd><pre>/**\n * Should the application auto-discover the requested resource.\n */\n\n<small>Inherited from CodeIgniter\\Modules\\Modules\nDefined in .../Modules/Modules.php:54</small></pre></dd></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public static</var> <dfn>CodeIgniter\\Modules\\Modules::__set_state(array $array)</dfn></dt><dd><pre><small>Defined in .../Modules/Modules.php:63</small></pre></dd></dl></li></ul></dd></dl></li></ul></dd></dl><dl><dt><var>protected</var> <dfn>saveData</dfn> -&gt; <var>boolean</var> true</dt></dl><dl><dt><var>protected</var> <dfn>viewsCount</dfn> -&gt; <var>integer</var> 1</dt></dl><dl><dt><var>protected</var> <dfn>layout</dfn> -&gt; <var>null</var></dt></dl><dl><dt><var>protected</var> <dfn>sections</dfn> -&gt; <var>array</var> (0)</dt></dl><dl><dt><var>protected</var> <dfn>sectionStack</dfn> -&gt; <var>array</var> (0)</dt></dl></li><li><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>__construct(Config\\View $config, ?string $viewPath = null, ?CodeIgniter\\Autoloader\\FileLocatorInterface $loader = null, ?bool $debug = null, ?Psr\\Log\\LoggerInterface $logger = null)</dfn></dt><dd><pre><small>Defined in .../View/View.php:135</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>render(string $view, ?array $options = null, ?bool $saveData = null)</dfn>: <var>string</var> Builds the output based upon a file name and any data that has already been set.</dt><dd><pre>/**\n * Builds the output based upon a file name and any\n * data that has already been set.\n *\n * Valid $options:\n *  - cache      Number of seconds to cache for\n *  - cache_name Name to use for cache\n *\n * @param string                    $view     File name of the view source\n * @param array&lt;string, mixed&gt;|null $options  Reserved for 3rd-party uses since\n *                                            it might be needed to pass additional info\n *                                            to other template engines.\n * @param bool|null                 $saveData If true, saves data for subsequent calls,\n *                                            if false, cleans the data after displaying,\n *                                            if null, uses the config setting.\n */\n\n<small>Defined in .../View/View.php:166</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>renderString(string $view, ?array $options = null, ?bool $saveData = null)</dfn>: <var>string</var> Builds the output based upon a string and any data that has already been set....</dt><dd><pre>/**\n * Builds the output based upon a string and any\n * data that has already been set.\n * Cache does not apply, because there is no \"key\".\n *\n * @param string                    $view     The view contents\n * @param array&lt;string, mixed&gt;|null $options  Reserved for 3rd-party uses since\n *                                            it might be needed to pass additional info\n *                                            to other template engines.\n * @param bool|null                 $saveData If true, saves data for subsequent calls,\n *                                            if false, cleans the data after displaying,\n *                                            if null, uses the config setting.\n */\n\n<small>Defined in .../View/View.php:307</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>excerpt(string $string, int $length = 20)</dfn>: <var>string</var> Extract first bit of a long string and add ellipsis</dt><dd><pre>/**\n * Extract first bit of a long string and add ellipsis\n */\n\n<small>Defined in .../View/View.php:330</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setData(array $data = array(), ?string $context = null)</dfn>: <var>CodeIgniter\\View\\RendererInterface</var> Sets several pieces of view data at once.</dt><dd><pre>/**\n * Sets several pieces of view data at once.\n *\n * @param         non-empty-string|null                     $context The context to escape it for.\n *                                                                   If 'raw', no escaping will happen.\n * @phpstan-param null|'html'|'js'|'css'|'url'|'attr'|'raw' $context\n */\n\n<small>Defined in .../View/View.php:342</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>setVar(string $name, $value = null, ?string $context = null)</dfn>: <var>CodeIgniter\\View\\RendererInterface</var> Sets a single piece of view data.</dt><dd><pre>/**\n * Sets a single piece of view data.\n *\n * @param         mixed                                     $value\n * @param         non-empty-string|null                     $context The context to escape it for.\n *                                                                   If 'raw', no escaping will happen.\n * @phpstan-param null|'html'|'js'|'css'|'url'|'attr'|'raw' $context\n */\n\n<small>Defined in .../View/View.php:362</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>resetData()</dfn>: <var>CodeIgniter\\View\\RendererInterface</var> Removes all of the view data from the system.</dt><dd><pre>/**\n * Removes all of the view data from the system.\n */\n\n<small>Defined in .../View/View.php:377</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getData()</dfn>: <var>array</var> Returns the current data that will be displayed in the view.</dt><dd><pre>/**\n * Returns the current data that will be displayed in the view.\n *\n * @return array&lt;string, mixed&gt;\n */\n\n<small>Defined in .../View/View.php:389</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>extend(string $layout)</dfn>: <var>void</var> Specifies that the current view should extend an existing layout.</dt><dd><pre>/**\n * Specifies that the current view should extend an existing layout.\n *\n * @return void\n */\n\n<small>Defined in .../View/View.php:399</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>section(string $name)</dfn>: <var>void</var> Starts holds content for a section within the layout.</dt><dd><pre>/**\n * Starts holds content for a section within the layout.\n *\n * @param string $name Section name\n *\n * @return void\n */\n\n<small>Defined in .../View/View.php:411</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>endSection()</dfn>: <var>void</var> Captures the last section</dt><dd><pre>/**\n * Captures the last section\n *\n * @return void\n *\n * @throws RuntimeException\n */\n\n<small>Defined in .../View/View.php:425</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>renderSection(string $sectionName, bool $saveData = false)</dfn>: <var>string</var> Renders a section's contents.</dt><dd><pre>/**\n * Renders a section's contents.\n *\n * @param bool $saveData If true, saves data for subsequent calls,\n *                       if false, cleans the data after displaying.\n */\n\n<small>Defined in .../View/View.php:449</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>include(string $view, ?array $options = null, $saveData = true)</dfn>: <var>string</var> Used within layout views to include additional views.</dt><dd><pre>/**\n * Used within layout views to include additional views.\n *\n * @param array&lt;string, mixed&gt;|null $options\n * @param bool                      $saveData\n */\n\n<small>Defined in .../View/View.php:473</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>public</var> <dfn>getPerformanceData()</dfn>: <var>array</var> Returns the performance data that might have been collected during the execut...</dt><dd><pre>/**\n * Returns the performance data that might have been collected\n * during the execution. Used primarily in the Debug Toolbar.\n *\n * @return list&lt;array{start: float, end: float, view: string}&gt;\n */\n\n<small>Defined in .../View/View.php:484</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>logPerformance(float $start, float $end, string $view)</dfn>: <var>void</var> Logs performance data for rendering a view.</dt><dd><pre>/**\n * Logs performance data for rendering a view.\n *\n * @return void\n */\n\n<small>Defined in .../View/View.php:494</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>prepareTemplateData(bool $saveData)</dfn>: <var>void</var></dt><dd><pre><small>Defined in .../View/View.php:505</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>decorateOutput(string $html)</dfn>: <var>string</var> Runs the generated output through any declared view decorators.</dt><dd><pre>/**\n * Runs the generated output through any declared\n * view decorators.\n */\n\n<small>Defined in .../View/ViewDecoratorTrait.php:25</small></pre></dd></dl></li></ul></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>only</dfn> -&gt; <var>array</var> (1)</dt><dd><dl><dt><dfn>0</dfn> =&gt; <var>string</var> (1) \"q\"</dt></dl></dd></dl></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>__construct(Config\\Pager $config, CodeIgniter\\View\\RendererInterface $view)</dfn> Constructor.<div class=\"access-path\">new \\CodeIgniter\\Pager\\Pager()</div></dt><dd><pre>/**\n * Constructor.\n */\n\n<small>Defined in .../Pager/Pager.php:72</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>links(string $group = 'default', string $template = 'default_full')</dfn>: <var>string</var> Handles creating and displaying the<div class=\"access-path\">$value-&gt;links()</div></dt><dd><pre>/**\n * Handles creating and displaying the\n *\n * @param string $template The output template alias to render.\n */\n\n<small>Defined in .../Pager/Pager.php:83</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>simpleLinks(string $group = 'default', string $template = 'default_simple')</dfn>: <var>string</var> Creates simple Next/Previous links, instead of full pagination.<div class=\"access-path\">$value-&gt;simpleLinks()</div></dt><dd><pre>/**\n * Creates simple Next/Previous links, instead of full pagination.\n */\n\n<small>Defined in .../Pager/Pager.php:93</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>makeLinks(int $page, ?int $perPage, int $total, string $template = 'default_full', int $segment = 0, ?string $group = 'default')</dfn>: <var>string</var> Allows for a simple, manual, form of pagination where all of the data is prov...<div class=\"access-path\">$value-&gt;makeLinks()</div></dt><dd><pre>/**\n * Allows for a simple, manual, form of pagination where all of the data\n * is provided by the user. The URL is the current URI.\n *\n * @param string      $template The output template alias to render.\n * @param int         $segment  (whether page number is provided by URI segment)\n * @param string|null $group    optional group (i.e. if we'd like to define custom path)\n */\n\n<small>Defined in .../Pager/Pager.php:108</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>displayLinks(string $group, string $template)</dfn>: <var>string</var> Does the actual work of displaying the view file. Used internally by links(),...</dt><dd><pre>/**\n * Does the actual work of displaying the view file. Used internally\n * by links(), simpleLinks(), and makeLinks().\n */\n\n<small>Defined in .../Pager/Pager.php:121</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>store(string $group, int $page, ?int $perPage, int $total, int $segment = 0)</dfn>: <var>$this</var> Stores a set of pagination data for later display. Most commonly used by the ...<div class=\"access-path\">$value-&gt;store()</div></dt><dd><pre>/**\n * Stores a set of pagination data for later display. Most commonly used\n * by the model to automate the process.\n *\n * @return $this\n */\n\n<small>Defined in .../Pager/Pager.php:139</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>setSegment(int $number, string $group = 'default')</dfn>: <var>$this</var> Sets segment for a group.<div class=\"access-path\">$value-&gt;setSegment()</div></dt><dd><pre>/**\n * Sets segment for a group.\n *\n * @return $this\n */\n\n<small>Defined in .../Pager/Pager.php:167</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>setPath(string $path, string $group = 'default')</dfn>: <var>$this</var> Sets the path that an aliased group of links will use.<div class=\"access-path\">$value-&gt;setPath()</div></dt><dd><pre>/**\n * Sets the path that an aliased group of links will use.\n *\n * @return $this\n */\n\n<small>Defined in .../Pager/Pager.php:183</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getTotal(string $group = 'default')</dfn>: <var>int</var> Returns the total number of items in data store.<div class=\"access-path\">$value-&gt;getTotal()</div></dt><dd><pre>/**\n * Returns the total number of items in data store.\n */\n\n<small>Defined in .../Pager/Pager.php:195</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getPageCount(string $group = 'default')</dfn>: <var>int</var> Returns the total number of pages.<div class=\"access-path\">$value-&gt;getPageCount()</div></dt><dd><pre>/**\n * Returns the total number of pages.\n */\n\n<small>Defined in .../Pager/Pager.php:205</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getCurrentPage(string $group = 'default')</dfn>: <var>int</var> Returns the number of the current page of results.<div class=\"access-path\">$value-&gt;getCurrentPage()</div></dt><dd><pre>/**\n * Returns the number of the current page of results.\n */\n\n<small>Defined in .../Pager/Pager.php:215</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>hasMore(string $group = 'default')</dfn>: <var>bool</var> Tells whether this group of results has any more pages of results.<div class=\"access-path\">$value-&gt;hasMore()</div></dt><dd><pre>/**\n * Tells whether this group of results has any more pages of results.\n */\n\n<small>Defined in .../Pager/Pager.php:225</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getLastPage(string $group = 'default')</dfn>: <var>int|null</var> Returns the last page, if we have a total that we can calculate with.<div class=\"access-path\">$value-&gt;getLastPage()</div></dt><dd><pre>/**\n * Returns the last page, if we have a total that we can calculate with.\n *\n * @return int|null\n */\n\n<small>Defined in .../Pager/Pager.php:237</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getFirstPage(string $group = 'default')</dfn>: <var>int</var> Determines the first page # that should be shown.<div class=\"access-path\">$value-&gt;getFirstPage()</div></dt><dd><pre>/**\n * Determines the first page # that should be shown.\n */\n\n<small>Defined in .../Pager/Pager.php:251</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getPageURI(?int $page = null, string $group = 'default', bool $returnObject = false)</dfn>: <var>string|URI</var> Returns the URI for a specific page for the specified group.<div class=\"access-path\">$value-&gt;getPageURI()</div></dt><dd><pre>/**\n * Returns the URI for a specific page for the specified group.\n *\n * @return string|URI\n */\n\n<small>Defined in .../Pager/Pager.php:264</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getNextPageURI(string $group = 'default', bool $returnObject = false)</dfn>: <var>string|null</var> Returns the full URI to the next page of results, or null.<div class=\"access-path\">$value-&gt;getNextPageURI()</div></dt><dd><pre>/**\n * Returns the full URI to the next page of results, or null.\n *\n * @return string|null\n */\n\n<small>Defined in .../Pager/Pager.php:307</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getPreviousPageURI(string $group = 'default', bool $returnObject = false)</dfn>: <var>string|null</var> Returns the full URL to the previous page of results, or null.<div class=\"access-path\">$value-&gt;getPreviousPageURI()</div></dt><dd><pre>/**\n * Returns the full URL to the previous page of results, or null.\n *\n * @return string|null\n */\n\n<small>Defined in .../Pager/Pager.php:331</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getPerPage(string $group = 'default')</dfn>: <var>int</var> Returns the number of results per page that should be shown.<div class=\"access-path\">$value-&gt;getPerPage()</div></dt><dd><pre>/**\n * Returns the number of results per page that should be shown.\n */\n\n<small>Defined in .../Pager/Pager.php:353</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>getDetails(string $group = 'default')</dfn>: <var>array</var> Returns an array with details about the results, including total, per_page, c...<div class=\"access-path\">$value-&gt;getDetails()</div></dt><dd><pre>/**\n * Returns an array with details about the results, including\n * total, per_page, current_page, last_page, next_url, prev_url, from, to.\n * Does not include the actual data. This data is suitable for adding\n * a 'data' object to with the result set and converting to JSON.\n */\n\n<small>Defined in .../Pager/Pager.php:366</small></pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><var>public</var> <dfn>only(array $queries)</dfn>: <var>self</var> Sets only allowed queries on pagination links.<div class=\"access-path\">$value-&gt;only()</div></dt><dd><pre>/**\n * Sets only allowed queries on pagination links.\n */\n\n<small>Defined in .../Pager/Pager.php:384</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>ensureGroup(string $group, ?int $perPage = null)</dfn>: <var>void</var> Ensures that an array exists for the group specified.</dt><dd><pre>/**\n * Ensures that an array exists for the group specified.\n *\n * @return void\n */\n\n<small>Defined in .../Pager/Pager.php:396</small></pre></dd></dl><dl><dt class=\"kint-parent\"><nav></nav><var>protected</var> <dfn>calculateCurrentPage(string $group)</dfn>: <var>void</var> Calculating the current page</dt><dd><pre>/**\n * Calculating the current page\n *\n * @return void\n */\n\n<small>Defined in .../Pager/Pager.php:424</small></pre></dd></dl></li></ul></dd></dl></div>"}}, "session": {"__ci_last_regenerate": "<pre>1750883515</pre>", "_ci_previous_url": "http://localhost:8080/index.php/admin/artikel?page=2", "user_id": "4", "user_name": "admin", "user_email": "<EMAIL>", "logged_in": "<pre>1</pre>"}, "get": {"page": "2"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id,en-US;q=0.9,en;q=0.8", "Cookie": "ci_session=d09b2f580c87d85841cec13a4ea0bc66"}, "cookies": {"ci_session": "d09b2f580c87d85841cec13a4ea0bc66"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}