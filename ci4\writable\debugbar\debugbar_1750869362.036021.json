{"url": "http://localhost:8080/index.php/fix/articles", "method": "GET", "isAJAX": false, "startTime": **********.88636, "totalTime": 127.8, "totalMemory": "5.094", "segmentDuration": 20, "segmentCount": 7, "CI_VERSION": "4.6.0", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.891, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.929221, "duration": 0.003971099853515625}, {"name": "Routing", "component": "Timer", "start": **********.933199, "duration": 0.0059680938720703125}, {"name": "Before Filters", "component": "Timer", "start": **********.9397, "duration": 3.600120544433594e-05}, {"name": "Controller", "component": "Timer", "start": **********.93974, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.939742, "duration": 0.0013909339904785156}, {"name": "After Filters", "component": "Timer", "start": **********.01327, "duration": 5.0067901611328125e-06}, {"name": "Required After Filters", "component": "Timer", "start": **********.013295, "duration": 0.0009000301361083984}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(17 total Queries, 14 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\FixData.php:14", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:14", "qid": "cbede05ac5bea15a5da0b5b0930995f1"}, {"hover": "", "class": "", "duration": "4.3 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 13&#039;, &#039;Ini adalah isi artikel sample nomor 13. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-13&#039;, 1, &#039;&#039;, 13)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "cbf15dc909fe10a65e288c6e0b661de6"}, {"hover": "", "class": "", "duration": "1.64 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 14&#039;, &#039;Ini adalah isi artikel sample nomor 14. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-14&#039;, 1, &#039;&#039;, 14)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "98ec02fa67c24e5e6141ad8a266f56b5"}, {"hover": "", "class": "", "duration": "1.63 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 15&#039;, &#039;Ini adalah isi artikel sample nomor 15. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-15&#039;, 1, &#039;&#039;, 15)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "c4c1604dc209c1a9ac0eb95e37a0bbd9"}, {"hover": "", "class": "", "duration": "2.77 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 16&#039;, &#039;Ini adalah isi artikel sample nomor 16. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-16&#039;, 1, &#039;&#039;, 16)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "17d722aa0ff34f00cd827d2c0e1e8569"}, {"hover": "", "class": "", "duration": "2.44 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 17&#039;, &#039;Ini adalah isi artikel sample nomor 17. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-17&#039;, 1, &#039;&#039;, 17)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "a41e863919f03320030ef280be92efdd"}, {"hover": "", "class": "", "duration": "1.61 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 18&#039;, &#039;Ini adalah isi artikel sample nomor 18. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-18&#039;, 1, &#039;&#039;, 18)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "3f50b7c5a218fa90ff2cba88960369b1"}, {"hover": "", "class": "", "duration": "1.6 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 19&#039;, &#039;Ini adalah isi artikel sample nomor 19. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-19&#039;, 1, &#039;&#039;, 19)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "1eb0ed5884173d48e704f8f84793458d"}, {"hover": "", "class": "", "duration": "1.46 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 20&#039;, &#039;Ini adalah isi artikel sample nomor 20. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-20&#039;, 1, &#039;&#039;, 20)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "4070ebc1568b189ec9bf442d45e7d0d3"}, {"hover": "", "class": "", "duration": "1.49 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 21&#039;, &#039;Ini adalah isi artikel sample nomor 21. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-21&#039;, 1, &#039;&#039;, 21)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "f571bcd6108f3effc9a77b93c4f79e81"}, {"hover": "", "class": "", "duration": "1.46 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 22&#039;, &#039;Ini adalah isi artikel sample nomor 22. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-22&#039;, 1, &#039;&#039;, 22)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "8c3cc6312912e48aaecf0e51fe1fb7fc"}, {"hover": "", "class": "", "duration": "1.48 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 23&#039;, &#039;Ini adalah isi artikel sample nomor 23. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-23&#039;, 1, &#039;&#039;, 23)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "bf775efeaaa9bd305a126d1eb0c294e6"}, {"hover": "", "class": "", "duration": "1.82 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 24&#039;, &#039;Ini adalah isi artikel sample nomor 24. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-24&#039;, 1, &#039;&#039;, 24)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "0cdfea356ac0af8b00e4ebd332080218"}, {"hover": "", "class": "", "duration": "1.61 ms", "sql": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 25&#039;, &#039;Ini adalah isi artikel sample nomor 25. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-25&#039;, 1, &#039;&#039;, 25)", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:2345", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:394", "function": "        CodeIgniter\\Database\\BaseBuilder->insert()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:839", "function": "        CodeIgniter\\Model->doInsert()", "index": "  3    "}, {"file": "SYSTEMPATH\\Model.php:800", "function": "        CodeIgniter\\BaseModel->insert()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\FixData.php:33", "function": "        CodeIgniter\\Model->insert()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:33", "qid": "555cdd19eb2bc77798386c8e2b04d742"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\FixData.php:36", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:36", "qid": "e02f98a21f1be94a3e8703bf6464c6e5"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\FixData.php:45", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:45", "qid": "d4b9d6b63bd30c91d76a6f7e0ce33faf"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1678", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:928", "function": "        CodeIgniter\\Database\\BaseBuilder->countAll()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\FixData.php:48", "function": "        CodeIgniter\\Model->__call()", "index": "  3    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\FixData->articles()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH\\index.php:56", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH\\Controllers\\FixData.php:48", "qid": "94f5edc7720e7170b84d243cec1f0d9b"}]}, "badgeValue": 17, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.956148, "duration": "0.024162"}, {"name": "Query", "component": "Database", "start": **********.981463, "duration": "0.000348", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`"}, {"name": "Query", "component": "Database", "start": **********.984883, "duration": "0.004300", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 13&#039;, &#039;Ini adalah isi artikel sample nomor 13. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-13&#039;, 1, &#039;&#039;, 13)"}, {"name": "Query", "component": "Database", "start": **********.989363, "duration": "0.001640", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 14&#039;, &#039;Ini adalah isi artikel sample nomor 14. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-14&#039;, 1, &#039;&#039;, 14)"}, {"name": "Query", "component": "Database", "start": **********.991136, "duration": "0.001629", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 15&#039;, &#039;Ini adalah isi artikel sample nomor 15. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-15&#039;, 1, &#039;&#039;, 15)"}, {"name": "Query", "component": "Database", "start": **********.992923, "duration": "0.002769", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 16&#039;, &#039;Ini adalah isi artikel sample nomor 16. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-16&#039;, 1, &#039;&#039;, 16)"}, {"name": "Query", "component": "Database", "start": **********.995868, "duration": "0.002445", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 17&#039;, &#039;Ini adalah isi artikel sample nomor 17. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-17&#039;, 1, &#039;&#039;, 17)"}, {"name": "Query", "component": "Database", "start": **********.998472, "duration": "0.001606", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 18&#039;, &#039;Ini adalah isi artikel sample nomor 18. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-18&#039;, 1, &#039;&#039;, 18)"}, {"name": "Query", "component": "Database", "start": **********.000225, "duration": "0.001598", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 19&#039;, &#039;Ini adalah isi artikel sample nomor 19. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-19&#039;, 1, &#039;&#039;, 19)"}, {"name": "Query", "component": "Database", "start": **********.001947, "duration": "0.001458", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 20&#039;, &#039;Ini adalah isi artikel sample nomor 20. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-20&#039;, 1, &#039;&#039;, 20)"}, {"name": "Query", "component": "Database", "start": **********.003524, "duration": "0.001486", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 21&#039;, &#039;Ini adalah isi artikel sample nomor 21. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-21&#039;, 1, &#039;&#039;, 21)"}, {"name": "Query", "component": "Database", "start": **********.005141, "duration": "0.001463", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 22&#039;, &#039;Ini adalah isi artikel sample nomor 22. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-22&#039;, 1, &#039;&#039;, 22)"}, {"name": "Query", "component": "Database", "start": **********.006725, "duration": "0.001478", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 23&#039;, &#039;Ini adalah isi artikel sample nomor 23. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-23&#039;, 1, &#039;&#039;, 23)"}, {"name": "Query", "component": "Database", "start": **********.00833, "duration": "0.001819", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 24&#039;, &#039;Ini adalah isi artikel sample nomor 24. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-24&#039;, 1, &#039;&#039;, 24)"}, {"name": "Query", "component": "Database", "start": **********.010408, "duration": "0.001613", "query": "<strong>INSERT</strong> <strong>INTO</strong> `artikel` (`judul`, `isi`, `slug`, `status`, `gambar`, `display_order`) <strong>VALUES</strong> (&#039;Artikel Sample 25&#039;, &#039;Ini adalah isi artikel sample nomor 25. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&#039;, &#039;artikel-sample-25&#039;, 1, &#039;&#039;, 25)"}, {"name": "Query", "component": "Database", "start": **********.0121, "duration": "0.000314", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`"}, {"name": "Query", "component": "Database", "start": **********.012509, "duration": "0.000255", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`"}, {"name": "Query", "component": "Database", "start": **********.012827, "duration": "0.000249", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows` <strong>FROM</strong> `artikel`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"get\" is deprecated. Use uppercase HTTP method like \"GET\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}, {"level": "warning", "msg": "[DEPRECATED] Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\". in SYSTEMPATH\\Router\\RouteCollection.php on line 1026.\n 1 SYSTEMPATH\\Router\\RouteCollection.php(1026): trigger_error('Passing lowercase HTTP method \"post\" is deprecated. Use uppercase HTTP method like \"POST\".', 16384)\n 2 APPPATH\\Config\\Routes.php(22): CodeIgniter\\Router\\RouteCollection->match([...], '/user/login', 'User::login')\n 3 SYSTEMPATH\\Router\\RouteCollection.php(339): require('C:\\\\xampp\\\\htdocs\\\\lab11_ci\\\\ci4\\\\app\\\\Config\\\\Routes.php')\n 4 SYSTEMPATH\\CodeIgniter.php(821): CodeIgniter\\Router\\RouteCollection->loadRoutes()\n 5 SYSTEMPATH\\CodeIgniter.php(455): CodeIgniter\\CodeIgniter->tryToRouteIt(null)\n 6 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 7 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 8 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 9 FCPATH\\index.php(56): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))"}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 0, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": []}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 140 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Escaper\\Escaper.php", "name": "Escaper.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LogLevel.php", "name": "LogLevel.php"}, {"path": "SYSTEMPATH\\ThirdParty\\PSR\\Log\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\FixData.php", "name": "FixData.php"}, {"path": "APPPATH\\Models\\ArtikelModel.php", "name": "ArtikelModel.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}]}, "badgeValue": 140, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\FixData", "method": "articles", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "about", "handler": "\\App\\Controllers\\Page::about"}, {"method": "GET", "route": "contact", "handler": "\\App\\Controllers\\Page::contact"}, {"method": "GET", "route": "faqs", "handler": "\\App\\Controllers\\Page::faqs"}, {"method": "GET", "route": "services", "handler": "\\App\\Controllers\\Page::services"}, {"method": "GET", "route": "artikel", "handler": "\\App\\Controllers\\artikel::index"}, {"method": "GET", "route": "artikel/(.*)", "handler": "\\App\\Controllers\\Artikel::view/$1"}, {"method": "GET", "route": "user", "handler": "\\App\\Controllers\\User::index"}, {"method": "GET", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "GET", "route": "user/logout", "handler": "\\App\\Controllers\\User::logout"}, {"method": "GET", "route": "cleanup/articles", "handler": "\\App\\Controllers\\Cleanup::articles"}, {"method": "GET", "route": "debug/pagination", "handler": "\\App\\Controllers\\Debug::pagination"}, {"method": "GET", "route": "fix/articles", "handler": "\\App\\Controllers\\FixData::articles"}, {"method": "GET", "route": "admin/artikel", "handler": "\\App\\Controllers\\Artikel::admin_index"}, {"method": "GET", "route": "admin/artikel/delete/(.*)", "handler": "\\App\\Controllers\\Artikel::delete/$1"}, {"method": "GET", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "GET", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "HEAD", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "HEAD", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "POST", "route": "user/login", "handler": "\\App\\Controllers\\User::login"}, {"method": "POST", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "POST", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PATCH", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PATCH", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "PUT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "PUT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "DELETE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "DELETE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "OPTIONS", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "OPTIONS", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "TRACE", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "TRACE", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CONNECT", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CONNECT", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}, {"method": "CLI", "route": "admin/artikel/add", "handler": "\\App\\Controllers\\Artikel::add"}, {"method": "CLI", "route": "admin/artikel/edit/(.*)", "handler": "\\App\\Controllers\\Artikel::edit/$1"}]}, "badgeValue": 18, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "13.84", "count": 1}, "dbquery": {"event": "db<PERSON><PERSON>", "duration": "0.40", "count": 17}}}, "badgeValue": 18, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.915369, "duration": 0.013844966888427734}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.981817, "duration": 2.288818359375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.989188, "duration": 2.3126602172851562e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.991008, "duration": 2.09808349609375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.992771, "duration": 2.7179718017578125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.9957, "duration": 2.7179718017578125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.998319, "duration": 2.7179718017578125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.000084, "duration": 2.4080276489257812e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.001827, "duration": 2.002716064453125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.003409, "duration": 1.71661376953125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.005015, "duration": 2.1219253540039062e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.006608, "duration": 1.9073486328125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.008207, "duration": 2.002716064453125e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.010161, "duration": 5.1975250244140625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.012027, "duration": 2.9087066650390625e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.012418, "duration": 1.7881393432617188e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.012767, "duration": 1.2874603271484375e-05}, {"name": "Event: db<PERSON>y", "component": "Events", "start": **********.013079, "duration": 1.4066696166992188e-05}]}], "vars": {"varData": {"View Data": []}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Microsoft Edge&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "id,en-US;q=0.9,en;q=0.8", "Cookie": "ci_session=6c4b6a031172a396f974334b0ab7958a"}, "cookies": {"ci_session": "6c4b6a031172a396f974334b0ab7958a"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Cache-Control": "no-store, max-age=0, no-cache", "Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.0", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}