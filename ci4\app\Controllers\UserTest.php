<?php

namespace App\Controllers;

use App\Models\UserModel;

class UserTest extends BaseController
{
    public function index()
    {
        $userModel = new UserModel();
        
        // Ambil semua user
        $users = $userModel->findAll();
        
        echo "<h1>Verifikasi Tabel User</h1>";
        echo "<h2>Data User:</h2>";
        
        if (!empty($users)) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Password (Hash)</th></tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['useremail']}</td>";
                echo "<td>" . substr($user['userpassword'], 0, 20) . "...</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            echo "<p><strong>Total User: " . count($users) . "</strong></p>";
        } else {
            echo "<p>Tidak ada data user.</p>";
        }
        
        echo "<h2>Struktur Tabel:</h2>";
        echo "<ul>";
        echo "<li>id: INT(11) AUTO_INCREMENT PRIMARY KEY</li>";
        echo "<li>username: VARCHAR(200) NOT NULL</li>";
        echo "<li>useremail: VARCHAR(200)</li>";
        echo "<li>userpassword: VARCHAR(200)</li>";
        echo "</ul>";
        
        echo "<p><a href='" . base_url() . "'>← Kembali ke Home</a></p>";
    }
}
