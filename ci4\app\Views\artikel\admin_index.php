<?= $this->include('template/admin_header'); ?>

<div class="admin-main">
    <div class="content-card">
        <h2>Daftar Artikel</h2>

        <form method="get" class="form-search">
            <input type="text" name="q" value="<?= $q; ?>" placeholder="Cari data">
            <input type="submit" value="Cari" class="btn btn-primary">
        </form>

        <table class="table">
        <thead>
            <tr>
                <th style="width: 50px;">No.</th>
                <th style="width: 50%;">Judul</th>
                <th style="width: 80px;">Status</th>
                <th style="width: 140px;">Aksi</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($artikel): ?>
                <?php foreach ($artikel as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['display_order'] ?? $row['id']); ?></td>
                        <td>
                            <strong><?= htmlspecialchars($row['judul']); ?></strong>
                            <br>
                            <small style="color: #666;"><?= nl2br(htmlspecialchars(substr($row['isi'], 0, 60))); ?>...</small>
                        </td>
                        <td>
                            <?php if ($row['status'] == 1): ?>
                                <span class="status-badge status-active">Aktif</span>
                            <?php else: ?>
                                <span class="status-badge status-inactive">Draft</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a class="btn-ubah" href="<?= base_url('/admin/artikel/edit/' . $row['id']); ?>">
                                Edit
                            </a>
                            <a class="btn-hapus" onclick="return confirm('Yakin ingin menghapus artikel ini?');" href="<?= base_url('/admin/artikel/delete/' . $row['id']); ?>">
                                Hapus
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="4" style="text-align: center; padding: 40px; color: #666;">
                        <strong>Belum ada artikel</strong><br>
                        <small>Klik tombol "Tambah Artikel" untuk membuat artikel pertama</small>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <!-- Pagination Container -->
    <div class="pagination-container">
        <!-- Debug Info (remove in production) -->
        <div style="background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; font-size: 12px;">
            <strong>Debug Info:</strong><br>
            Current Page: <?= $pager->getCurrentPage() ?><br>
            Total Pages: <?= $pager->getPageCount() ?><br>
            Per Page: <?= $pager->getPerPage() ?><br>
            Total Records: <?= $pager->getTotal() ?><br>
            Articles Count: <?= count($artikel) ?><br>
            Query: <?= $q ? "'{$q}'" : 'No search' ?>
        </div>

        <!-- Pagination Info -->
        <div class="pagination-info">
            <p>Showing <?= ($pager->getCurrentPage() - 1) * $pager->getPerPage() + 1 ?> to <?= min($pager->getCurrentPage() * $pager->getPerPage(), $pager->getTotal()) ?> of <?= $pager->getTotal() ?> articles</p>
        </div>

        <!-- Pagination Links -->
        <?= $pager->only(['q'])->links('simple_pagination'); ?>
    </div>
    </div>
</div>

<?= $this->include('template/admin_footer'); ?>