<?php

namespace App\Controllers;

use App\Models\ArtikelModel;

class Cleanup extends BaseController
{
    public function articles()
    {
        $model = new ArtikelModel();
        
        // Cek jumlah artikel saat ini
        $total = $model->countAll();
        
        echo "<h1>Cleanup Artikel</h1>";
        echo "<p>Total artikel saat ini: <strong>{$total}</strong></p>";
        
        if ($total > 25) {
            // Hapus artikel dengan ID > 25
            $db = \Config\Database::connect();
            $deleted = $db->query("DELETE FROM artikel WHERE id > 25");
            
            $newTotal = $model->countAll();
            echo "<p style='color: green;'>✅ Berhasil menghapus artikel berlebihan!</p>";
            echo "<p>Total artikel setelah cleanup: <strong>{$newTotal}</strong></p>";
            echo "<p>Sekarang ada {$newTotal} artikel (3 halaman dengan 10 artikel per halaman)</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ Artikel sudah sesuai (≤ 25), tidak perlu cleanup.</p>";
        }
        
        echo "<p><a href='" . base_url('/admin/artikel') . "'>← Kembali ke Admin</a></p>";
    }
}
